<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إدارة القاعات</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/general-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">إدارة القاعات</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  {{#if success}}
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    {{success}}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  {{/if}}

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-4">
              <a href="/general-manager/venues/create" class="btn btn-primary mb-2">
                <i class="mdi mdi-plus-circle me-1"></i>
                إنشاء قاعة جديدة
              </a>
            </div>
            <div class="col-sm-8">
              <div class="text-sm-end">
                <form class="d-flex" method="GET">
                  <input type="text" class="form-control me-2" name="search" placeholder="البحث في القاعات..." value="{{search}}">
                  <button class="btn btn-outline-secondary" type="submit">بحث</button>
                </form>
              </div>
            </div>
          </div>

          <div class="table-responsive">
            <table class="table table-centered table-striped dt-responsive nowrap w-100">
              <thead>
                <tr>
                  <th>اسم القاعة</th>
                  <th>الموقع</th>
                  <th>السعة</th>
                  <th>الطابق</th>
                  <th>السعر/ساعة</th>
                  <th>المدير</th>
                  <th>المنشئ</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {{#each venues}}
                <tr>
                  <td>
                    <strong>{{name}}</strong>
                    {{#if suiteName}}
                    <br><small class="text-muted">{{suiteName}}</small>
                    {{/if}}
                  </td>
                  <td>{{location}}</td>
                  <td>{{capacity}} شخص</td>
                  <td>{{#if floor}}الطابق {{floor}}{{else}}-{{/if}}</td>
                  <td>{{#if pricePerHour}}{{pricePerHour}} ريال{{else}}-{{/if}}</td>
                  <td>
                    {{#if manager}}
                    {{manager.fullName}}
                    <br><small class="text-muted">{{manager.email}}</small>
                    {{else}}
                    <span class="badge bg-warning">غير مُعيَّن</span>
                    {{/if}}
                  </td>
                  <td>{{creator.fullName}}</td>
                  <td>
                    {{#if isActive}}
                    <span class="badge bg-success">نشط</span>
                    {{else}}
                    <span class="badge bg-danger">غير نشط</span>
                    {{/if}}
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewVenue('{{id}}')">
                        <i class="mdi mdi-eye"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-warning" onclick="editVenue('{{id}}')">
                        <i class="mdi mdi-pencil"></i>
                      </button>
                      {{#unless isActive}}
                      <button type="button" class="btn btn-sm btn-outline-success" onclick="activateVenue('{{id}}')">
                        <i class="mdi mdi-check"></i>
                      </button>
                      {{else}}
                      <button type="button" class="btn btn-sm btn-outline-danger" onclick="deactivateVenue('{{id}}')">
                        <i class="mdi mdi-close"></i>
                      </button>
                      {{/unless}}
                    </div>
                  </td>
                </tr>
                {{else}}
                <tr>
                  <td colspan="9" class="text-center">لا توجد قاعات</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>

          {{#if pagination}}
          <div class="row">
            <div class="col-12">
              <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                  {{#if pagination.hasPrev}}
                  <li class="page-item">
                    <a class="page-link" href="?page={{subtract pagination.currentPage 1}}">السابق</a>
                  </li>
                  {{/if}}
                  
                  <li class="page-item active">
                    <span class="page-link">{{pagination.currentPage}} من {{pagination.totalPages}}</span>
                  </li>
                  
                  {{#if pagination.hasNext}}
                  <li class="page-item">
                    <a class="page-link" href="?page={{add pagination.currentPage 1}}">التالي</a>
                  </li>
                  {{/if}}
                </ul>
              </nav>
            </div>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Venue Details Modal -->
<div class="modal fade" id="venueModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل القاعة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="venueModalBody">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

<script>
function viewVenue(venueId) {
  // Load venue details
  fetch(`/general-manager/venues/${venueId}`)
    .then(response => response.json())
    .then(data => {
      document.getElementById('venueModalBody').innerHTML = `
        <div class="row">
          <div class="col-md-6">
            <h6>معلومات أساسية</h6>
            <p><strong>الاسم:</strong> ${data.name}</p>
            <p><strong>الموقع:</strong> ${data.location}</p>
            <p><strong>السعة:</strong> ${data.capacity} شخص</p>
            <p><strong>الطابق:</strong> ${data.floor || 'غير محدد'}</p>
          </div>
          <div class="col-md-6">
            <h6>تفاصيل إضافية</h6>
            <p><strong>عدد الطاولات:</strong> ${data.tableCount || 'غير محدد'}</p>
            <p><strong>عدد الكراسي:</strong> ${data.chairCount || 'غير محدد'}</p>
            <p><strong>السعر/ساعة:</strong> ${data.pricePerHour ? data.pricePerHour + ' ريال' : 'غير محدد'}</p>
            <p><strong>اسم الجناح:</strong> ${data.suiteName || 'غير محدد'}</p>
          </div>
        </div>
        ${data.description ? `<div class="row"><div class="col-12"><h6>الوصف</h6><p>${data.description}</p></div></div>` : ''}
        ${data.amenities ? `<div class="row"><div class="col-12"><h6>المرافق</h6><p>${data.amenities}</p></div></div>` : ''}
      `;
      new bootstrap.Modal(document.getElementById('venueModal')).show();
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل تفاصيل القاعة');
    });
}

function editVenue(venueId) {
  window.location.href = `/general-manager/venues/${venueId}/edit`;
}

function activateVenue(venueId) {
  if (confirm('هل أنت متأكد من تفعيل هذه القاعة؟')) {
    updateVenueStatus(venueId, true);
  }
}

function deactivateVenue(venueId) {
  if (confirm('هل أنت متأكد من إلغاء تفعيل هذه القاعة؟')) {
    updateVenueStatus(venueId, false);
  }
}

function updateVenueStatus(venueId, isActive) {
  fetch(`/general-manager/venues/${venueId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ isActive })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert(data.error || 'حدث خطأ');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في تحديث حالة القاعة');
  });
}
</script>
