'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('parties', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      type: {
        type: Sequelize.ENUM('wedding', 'birthday', 'corporate', 'graduation', 'anniversary', 'other'),
        allowNull: false,
        defaultValue: 'other'
      },
      party_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      start_time: {
        type: Sequelize.TIME,
        allowNull: false
      },
      end_time: {
        type: Sequelize.TIME,
        allowNull: false
      },
      expected_guests: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      actual_guests: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      total_cost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('planned', 'confirmed', 'active', 'completed', 'cancelled'),
        allowNull: false,
        defaultValue: 'planned'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      venue_id: {
        type: Sequelize.UUID,
        allowNull: false
      },
      owner_id: {
        type: Sequelize.UUID,
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('parties');
  }
};
