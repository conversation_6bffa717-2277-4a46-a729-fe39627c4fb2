<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إنشاء مدير قاعة جديد</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/general-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="/general-manager/venue-managers">إدارة مديري القاعات</a></li>
            <li class="breadcrumb-item active">إنشاء مدير قاعة جديد</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8 offset-lg-2">
      <div class="card">
        <div class="card-body">
          {{#if error}}
          <div class="alert alert-danger" role="alert">
            {{error}}
          </div>
          {{/if}}

          {{#if errors}}
          <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
              {{#each errors}}
              <li>{{msg}}</li>
              {{/each}}
            </ul>
          </div>
          {{/if}}

          <form method="POST" action="/general-manager/venue-managers/create">
            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="fullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="fullName" name="fullName" value="{{formData.fullName}}" required>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                  <input type="email" class="form-control" id="email" name="email" value="{{formData.email}}" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="phone" class="form-label">رقم الهاتف</label>
                  <input type="tel" class="form-control" id="phone" name="phone" value="{{formData.phone}}" placeholder="05xxxxxxxx">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <input type="password" class="form-control" id="password" name="password" required minlength="6">
                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                      <i class="mdi mdi-eye" id="password-icon"></i>
                    </button>
                  </div>
                  <small class="form-text text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="confirmPassword" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirmPassword')">
                      <i class="mdi mdi-eye" id="confirmPassword-icon"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="managedVenueId" class="form-label">القاعة المُدارة (اختياري)</label>
                  <select class="form-select" id="managedVenueId" name="managedVenueId">
                    <option value="">اختر قاعة...</option>
                    {{#each availableVenues}}
                    <option value="{{id}}" {{#if (eq id ../formData.managedVenueId)}}selected{{/if}}>{{name}} - {{location}}</option>
                    {{/each}}
                  </select>
                  <small class="form-text text-muted">يمكن تعيين القاعة لاحقاً من صفحة إدارة المديرين</small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="alert alert-info" role="alert">
                  <h6 class="alert-heading">ملاحظة مهمة:</h6>
                  <p class="mb-0">
                    سيتم إنشاء حساب مدير قاعة جديد بالمعلومات المدخلة. سيتمكن المدير من:
                  </p>
                  <ul class="mb-0 mt-2">
                    <li>إدارة الحفلات في القاعة المُعيَّنة له</li>
                    <li>تعيين أصحاب الحفلات</li>
                    <li>تعيين موظفين (ماسحين ضوئيين)</li>
                    <li>متابعة تقارير القاعة</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="text-end">
                  <a href="/general-manager/venue-managers" class="btn btn-secondary me-2">إلغاء</a>
                  <button type="submit" class="btn btn-primary">إنشاء مدير القاعة</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function togglePassword(fieldId) {
  const field = document.getElementById(fieldId);
  const icon = document.getElementById(fieldId + '-icon');
  
  if (field.type === 'password') {
    field.type = 'text';
    icon.className = 'mdi mdi-eye-off';
  } else {
    field.type = 'password';
    icon.className = 'mdi mdi-eye';
  }
}

document.addEventListener('DOMContentLoaded', function() {
  const form = document.querySelector('form');
  const password = document.getElementById('password');
  const confirmPassword = document.getElementById('confirmPassword');
  
  function validatePasswords() {
    if (password.value !== confirmPassword.value) {
      confirmPassword.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
      confirmPassword.setCustomValidity('');
    }
  }
  
  password.addEventListener('input', validatePasswords);
  confirmPassword.addEventListener('input', validatePasswords);
  
  form.addEventListener('submit', function(e) {
    validatePasswords();
    if (!confirmPassword.checkValidity()) {
      e.preventDefault();
      confirmPassword.reportValidity();
    }
  });
});
</script>
