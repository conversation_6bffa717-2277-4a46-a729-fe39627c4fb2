const sequelize = require('../config/database');

async function dropTables() {
  console.log('🔄 بدء حذف الجداول...');
  
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    const queryInterface = sequelize.getQueryInterface();

    // Drop tables in reverse order to avoid foreign key constraints
    const tables = ['invitations', 'parties', 'venues', 'users'];
    
    for (const table of tables) {
      try {
        await queryInterface.dropTable(table);
        console.log(`✅ تم حذف جدول ${table}`);
      } catch (error) {
        console.log(`⚠️  جدول ${table} غير موجود أو تم حذفه مسبقاً`);
      }
    }
    
    console.log('✅ تم حذف جميع الجداول بنجاح!');
    
  } catch (error) {
    console.error('❌ فشل في حذف الجداول:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run if called directly
if (require.main === module) {
  dropTables().then(() => {
    console.log('🎉 انتهت عملية حذف الجداول بنجاح!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ فشلت عملية حذف الجداول:', error);
    process.exit(1);
  });
}

module.exports = { dropTables };
