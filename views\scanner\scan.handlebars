<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">مسح رموز QR - {{assignedVenue.name}}</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/scanner/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">مسح رموز QR</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Scanner Interface -->
  <div class="row">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title mb-3">ماسح رموز QR</h4>
          
          <!-- Party Selection -->
          {{#if activeParties}}
          <div class="mb-4">
            <label for="selectedParty" class="form-label">اختر الحفلة النشطة</label>
            <select class="form-select" id="selectedParty" onchange="selectParty()">
              <option value="">اختر حفلة...</option>
              {{#each activeParties}}
              <option value="{{id}}" data-title="{{title}}" data-owner="{{owner.fullName}}">
                {{title}} - {{owner.fullName}} ({{startTime}} - {{endTime}})
              </option>
              {{/each}}
            </select>
          </div>
          {{/if}}

          <!-- QR Scanner -->
          <div class="text-center mb-4">
            <div id="qr-reader" style="width: 100%; max-width: 500px; margin: 0 auto;"></div>
            <div id="qr-reader-results"></div>
          </div>

          <!-- Manual Input -->
          <div class="card bg-light">
            <div class="card-body">
              <h6 class="card-title">إدخال يدوي</h6>
              <div class="row">
                <div class="col-md-8">
                  <input type="text" class="form-control" id="manualQRCode" placeholder="أدخل رمز QR يدوياً...">
                </div>
                <div class="col-md-4">
                  <button type="button" class="btn btn-primary w-100" onclick="processManualQR()">
                    <i class="mdi mdi-check me-1"></i>
                    تأكيد
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scan Results -->
    <div class="col-lg-4">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title">نتائج المسح</h4>
          
          <!-- Current Session Stats -->
          <div class="row text-center mb-3">
            <div class="col-6">
              <div class="card bg-success text-white">
                <div class="card-body p-2">
                  <h4 class="mb-0" id="successCount">0</h4>
                  <small>نجح</small>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="card bg-danger text-white">
                <div class="card-body p-2">
                  <h4 class="mb-0" id="errorCount">0</h4>
                  <small>فشل</small>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Scans -->
          <div id="recentScans">
            <h6>آخر المسح</h6>
            <div id="scansList" class="list-group">
              <!-- Recent scans will appear here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Selected Party Info -->
      <div class="card" id="partyInfoCard" style="display: none;">
        <div class="card-body">
          <h6 class="card-title">معلومات الحفلة</h6>
          <div id="partyInfo">
            <!-- Party info will be displayed here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Instructions -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">تعليمات الاستخدام</h5>
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-primary">للمسح بالكاميرا:</h6>
              <ol>
                <li>اختر الحفلة النشطة من القائمة</li>
                <li>اسمح للموقع بالوصول للكاميرا</li>
                <li>وجه الكاميرا نحو رمز QR</li>
                <li>انتظر حتى يتم المسح تلقائياً</li>
              </ol>
            </div>
            <div class="col-md-6">
              <h6 class="text-primary">للإدخال اليدوي:</h6>
              <ol>
                <li>اختر الحفلة النشطة من القائمة</li>
                <li>أدخل رمز QR في الحقل المخصص</li>
                <li>اضغط زر "تأكيد"</li>
                <li>تحقق من صحة البيانات</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title">
          <i class="mdi mdi-check-circle me-2"></i>
          تم تسجيل الحضور بنجاح
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="successModalBody">
        <!-- Success details will be shown here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-success" data-bs-dismiss="modal">موافق</button>
      </div>
    </div>
  </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title">
          <i class="mdi mdi-alert-circle me-2"></i>
          خطأ في المسح
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="errorModalBody">
        <!-- Error details will be shown here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" data-bs-dismiss="modal">موافق</button>
      </div>
    </div>
  </div>
</div>

<!-- Include QR Scanner Library -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

<script>
let html5QrcodeScanner;
let selectedPartyId = null;
let successCount = 0;
let errorCount = 0;

document.addEventListener('DOMContentLoaded', function() {
  // Initialize QR Scanner
  initializeQRScanner();
  
  // Check if party is pre-selected from URL
  const urlParams = new URLSearchParams(window.location.search);
  const partyParam = urlParams.get('party');
  if (partyParam) {
    document.getElementById('selectedParty').value = partyParam;
    selectParty();
  }
});

function initializeQRScanner() {
  html5QrcodeScanner = new Html5QrcodeScanner(
    "qr-reader",
    { 
      fps: 10, 
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0
    },
    false
  );
  
  html5QrcodeScanner.render(onScanSuccess, onScanFailure);
}

function onScanSuccess(decodedText, decodedResult) {
  processQRCode(decodedText);
}

function onScanFailure(error) {
  // Handle scan failure silently
}

function selectParty() {
  const select = document.getElementById('selectedParty');
  const selectedOption = select.options[select.selectedIndex];
  
  if (selectedOption.value) {
    selectedPartyId = selectedOption.value;
    
    // Show party info
    const partyInfo = document.getElementById('partyInfo');
    partyInfo.innerHTML = `
      <p><strong>العنوان:</strong> ${selectedOption.dataset.title}</p>
      <p><strong>صاحب الحفلة:</strong> ${selectedOption.dataset.owner}</p>
      <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
    `;
    
    document.getElementById('partyInfoCard').style.display = 'block';
  } else {
    selectedPartyId = null;
    document.getElementById('partyInfoCard').style.display = 'none';
  }
}

function processManualQR() {
  const qrCode = document.getElementById('manualQRCode').value.trim();
  if (qrCode) {
    processQRCode(qrCode);
    document.getElementById('manualQRCode').value = '';
  } else {
    alert('يرجى إدخال رمز QR');
  }
}

function processQRCode(qrCode) {
  if (!selectedPartyId) {
    showError('يرجى اختيار الحفلة أولاً');
    return;
  }

  // Show loading
  const loadingToast = showToast('جاري معالجة رمز QR...', 'info');

  fetch('/scanner/scan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      qrCode: qrCode,
      partyId: selectedPartyId
    })
  })
  .then(response => response.json())
  .then(data => {
    // Hide loading toast
    if (loadingToast) loadingToast.hide();
    
    if (data.success) {
      showSuccess(data);
      addToRecentScans(data.invitation, true);
      successCount++;
      document.getElementById('successCount').textContent = successCount;
    } else {
      showError(data.error, data.invitation);
      if (data.invitation) {
        addToRecentScans(data.invitation, false);
      }
      errorCount++;
      document.getElementById('errorCount').textContent = errorCount;
    }
  })
  .catch(error => {
    // Hide loading toast
    if (loadingToast) loadingToast.hide();
    
    console.error('Error:', error);
    showError('حدث خطأ في الاتصال بالخادم');
    errorCount++;
    document.getElementById('errorCount').textContent = errorCount;
  });
}

function showSuccess(data) {
  const modalBody = document.getElementById('successModalBody');
  modalBody.innerHTML = `
    <div class="text-center">
      <div class="avatar-lg mx-auto mb-4">
        <div class="avatar-title bg-success text-white rounded-circle">
          <i class="mdi mdi-check font-24"></i>
        </div>
      </div>
      <h5>مرحباً ${data.invitation.guestName}</h5>
      <p class="text-muted">تم تسجيل حضورك في حفلة: ${data.invitation.party}</p>
      <p class="text-muted">وقت الحضور: ${new Date(data.invitation.attendedAt).toLocaleString('ar-SA')}</p>
    </div>
  `;
  
  new bootstrap.Modal(document.getElementById('successModal')).show();
  
  // Play success sound
  playSound('success');
}

function showError(message, invitation = null) {
  const modalBody = document.getElementById('errorModalBody');
  let content = `<p class="text-center">${message}</p>`;
  
  if (invitation) {
    content += `
      <div class="mt-3">
        <h6>تفاصيل الدعوة:</h6>
        <p><strong>اسم الضيف:</strong> ${invitation.guestName}</p>
        <p><strong>الحفلة:</strong> ${invitation.party}</p>
        ${invitation.attendedAt ? `<p><strong>وقت الحضور السابق:</strong> ${new Date(invitation.attendedAt).toLocaleString('ar-SA')}</p>` : ''}
      </div>
    `;
  }
  
  modalBody.innerHTML = content;
  new bootstrap.Modal(document.getElementById('errorModal')).show();
  
  // Play error sound
  playSound('error');
}

function addToRecentScans(invitation, success) {
  const scansList = document.getElementById('scansList');
  const scanItem = document.createElement('div');
  scanItem.className = `list-group-item ${success ? 'list-group-item-success' : 'list-group-item-danger'}`;
  
  scanItem.innerHTML = `
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h6 class="mb-1">${invitation.guestName}</h6>
        <small>${new Date().toLocaleTimeString('ar-SA')}</small>
      </div>
      <i class="mdi mdi-${success ? 'check-circle text-success' : 'alert-circle text-danger'}"></i>
    </div>
  `;
  
  // Add to top of list
  scansList.insertBefore(scanItem, scansList.firstChild);
  
  // Keep only last 10 items
  while (scansList.children.length > 10) {
    scansList.removeChild(scansList.lastChild);
  }
}

function showToast(message, type = 'info') {
  // Simple toast implementation
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
  toast.textContent = message;
  
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 3000);
  
  return {
    hide: () => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }
  };
}

function playSound(type) {
  // Create audio context for sound feedback
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    if (type === 'success') {
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
    } else {
      oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(300, audioContext.currentTime + 0.1);
    }
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
  } catch (e) {
    // Sound not supported
  }
}
</script>
