<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إنشاء قاعة جديدة</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/general-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="/general-manager/venues">إدارة القاعات</a></li>
            <li class="breadcrumb-item active">إنشاء قاعة جديدة</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          {{#if error}}
          <div class="alert alert-danger" role="alert">
            {{error}}
          </div>
          {{/if}}

          {{#if errors}}
          <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
              {{#each errors}}
              <li>{{msg}}</li>
              {{/each}}
            </ul>
          </div>
          {{/if}}

          <form method="POST" action="/general-manager/venues/create">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="name" class="form-label">اسم القاعة <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="name" name="name" value="{{formData.name}}" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="suiteName" class="form-label">اسم الجناح</label>
                  <input type="text" class="form-control" id="suiteName" name="suiteName" value="{{formData.suiteName}}">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="location" class="form-label">الموقع <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="location" name="location" rows="2" required>{{formData.location}}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="capacity" class="form-label">السعة (عدد الأشخاص) <span class="text-danger">*</span></label>
                  <input type="number" class="form-control" id="capacity" name="capacity" value="{{formData.capacity}}" min="1" required>
                </div>
              </div>
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="floor" class="form-label">رقم الطابق</label>
                  <input type="number" class="form-control" id="floor" name="floor" value="{{formData.floor}}" min="0">
                </div>
              </div>
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="tableCount" class="form-label">عدد الطاولات</label>
                  <input type="number" class="form-control" id="tableCount" name="tableCount" value="{{formData.tableCount}}" min="0">
                </div>
              </div>
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="chairCount" class="form-label">عدد الكراسي</label>
                  <input type="number" class="form-control" id="chairCount" name="chairCount" value="{{formData.chairCount}}" min="0">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="pricePerHour" class="form-label">السعر بالساعة (ريال)</label>
                  <input type="number" class="form-control" id="pricePerHour" name="pricePerHour" value="{{formData.pricePerHour}}" min="0" step="0.01">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="description" class="form-label">الوصف</label>
                  <textarea class="form-control" id="description" name="description" rows="3">{{formData.description}}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="amenities" class="form-label">المرافق المتاحة</label>
                  <div class="row">
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="ac" name="amenities[]" value="مكيف">
                        <label class="form-check-label" for="ac">مكيف</label>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="sound" name="amenities[]" value="نظام صوتي">
                        <label class="form-check-label" for="sound">نظام صوتي</label>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="lighting" name="amenities[]" value="إضاءة">
                        <label class="form-check-label" for="lighting">إضاءة</label>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="stage" name="amenities[]" value="مسرح">
                        <label class="form-check-label" for="stage">مسرح</label>
                      </div>
                    </div>
                  </div>
                  <div class="row mt-2">
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="kitchen" name="amenities[]" value="مطبخ">
                        <label class="form-check-label" for="kitchen">مطبخ</label>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="parking" name="amenities[]" value="موقف سيارات">
                        <label class="form-check-label" for="parking">موقف سيارات</label>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="wifi" name="amenities[]" value="واي فاي">
                        <label class="form-check-label" for="wifi">واي فاي</label>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="security" name="amenities[]" value="أمن">
                        <label class="form-check-label" for="security">أمن</label>
                      </div>
                    </div>
                  </div>
                  <div class="mt-2">
                    <input type="text" class="form-control" placeholder="مرافق أخرى (اختياري)" name="customAmenities">
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="text-end">
                  <a href="/general-manager/venues" class="btn btn-secondary me-2">إلغاء</a>
                  <button type="submit" class="btn btn-primary">إنشاء القاعة</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle amenities checkboxes
  const form = document.querySelector('form');
  form.addEventListener('submit', function(e) {
    const checkboxes = document.querySelectorAll('input[name="amenities[]"]:checked');
    const customAmenities = document.querySelector('input[name="customAmenities"]').value;
    
    let amenities = Array.from(checkboxes).map(cb => cb.value);
    if (customAmenities) {
      amenities.push(customAmenities);
    }
    
    // Create hidden input for amenities
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'amenities';
    hiddenInput.value = amenities.join(', ');
    form.appendChild(hiddenInput);
    
    // Remove checkbox inputs to avoid conflicts
    checkboxes.forEach(cb => cb.remove());
    document.querySelector('input[name="customAmenities"]').remove();
  });
});
</script>
