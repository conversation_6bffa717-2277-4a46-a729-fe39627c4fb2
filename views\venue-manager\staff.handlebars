<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إدارة الموظفين - {{managedVenue.name}}</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/venue-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">إدارة الموظفين</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  {{#if success}}
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    {{success}}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  {{/if}}

  <!-- Venue Info -->
  <div class="row">
    <div class="col-12">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-8">
              <h5 class="text-white mb-1">{{managedVenue.name}}</h5>
              <p class="text-white-50 mb-0">{{managedVenue.location}}</p>
            </div>
            <div class="col-4 text-end">
              <h3 class="text-white mb-0">{{assignedScanners.length}}</h3>
              <p class="text-white-50 mb-0">ماسح ضوئي مُعيَّن</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Assigned Scanners -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-6">
              <h4 class="header-title">الماسحين الضوئيين المُعيَّنين</h4>
            </div>
            <div class="col-sm-6">
              <div class="text-sm-end">
                <a href="/venue-manager/staff/assign" class="btn btn-primary mb-2">
                  <i class="mdi mdi-account-plus me-1"></i>
                  تعيين ماسح ضوئي جديد
                </a>
              </div>
            </div>
          </div>

          {{#if assignedScanners}}
          <div class="table-responsive">
            <table class="table table-centered table-striped dt-responsive nowrap w-100">
              <thead>
                <tr>
                  <th>الاسم الكامل</th>
                  <th>البريد الإلكتروني</th>
                  <th>رقم الهاتف</th>
                  <th>تاريخ التعيين</th>
                  <th>الحالة</th>
                  <th>آخر نشاط</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {{#each assignedScanners}}
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-success d-flex align-items-center justify-content-center">
                          <span class="text-white">{{substring fullName 0 1}}</span>
                        </div>
                      </div>
                      <div class="flex-grow-1 ms-2">
                        <h5 class="my-0">{{fullName}}</h5>
                        <small class="text-muted">ماسح ضوئي</small>
                      </div>
                    </div>
                  </td>
                  <td>{{email}}</td>
                  <td>{{#if phone}}{{phone}}{{else}}-{{/if}}</td>
                  <td>{{formatDate createdAt}}</td>
                  <td>
                    {{#if isActive}}
                    <span class="badge bg-success">نشط</span>
                    {{else}}
                    <span class="badge bg-danger">غير نشط</span>
                    {{/if}}
                  </td>
                  <td>
                    <span class="text-muted">{{formatDate updatedAt}}</span>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewScanner('{{id}}')" title="عرض التفاصيل">
                        <i class="mdi mdi-eye"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-info" onclick="viewScannerActivity('{{id}}')" title="عرض النشاط">
                        <i class="mdi mdi-chart-line"></i>
                      </button>
                      {{#if isActive}}
                      <button type="button" class="btn btn-sm btn-outline-warning" onclick="deactivateScanner('{{id}}')" title="إلغاء التفعيل">
                        <i class="mdi mdi-pause"></i>
                      </button>
                      {{else}}
                      <button type="button" class="btn btn-sm btn-outline-success" onclick="activateScanner('{{id}}')" title="تفعيل">
                        <i class="mdi mdi-play"></i>
                      </button>
                      {{/if}}
                      <button type="button" class="btn btn-sm btn-outline-danger" onclick="unassignScanner('{{id}}')" title="إلغاء التعيين">
                        <i class="mdi mdi-account-remove"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>
          {{else}}
          <div class="text-center py-4">
            <div class="avatar-lg mx-auto mb-4">
              <div class="avatar-title bg-soft-primary text-primary rounded-circle">
                <i class="mdi mdi-account-search font-24"></i>
              </div>
            </div>
            <h5>لا يوجد ماسحين ضوئيين مُعيَّنين</h5>
            <p class="text-muted">لم يتم تعيين أي ماسح ضوئي لهذه القاعة بعد</p>
            <a href="/venue-manager/staff/assign" class="btn btn-primary">
              <i class="mdi mdi-account-plus me-1"></i>
              تعيين ماسح ضوئي
            </a>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>

  <!-- Staff Guidelines -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title">إرشادات إدارة الموظفين</h4>
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-primary">مسؤوليات الماسح الضوئي:</h6>
              <ul class="list-unstyled">
                <li><i class="mdi mdi-check text-success me-1"></i> مسح رموز QR للضيوف عند الوصول</li>
                <li><i class="mdi mdi-check text-success me-1"></i> تحديث حالة الدعوات إلى "حضر"</li>
                <li><i class="mdi mdi-check text-success me-1"></i> التحقق من صحة الدعوات</li>
                <li><i class="mdi mdi-check text-success me-1"></i> تسجيل أوقات الحضور</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6 class="text-primary">صلاحيات مدير القاعة:</h6>
              <ul class="list-unstyled">
                <li><i class="mdi mdi-check text-success me-1"></i> تعيين ماسحين ضوئيين للقاعة</li>
                <li><i class="mdi mdi-check text-success me-1"></i> إلغاء تعيين الماسحين</li>
                <li><i class="mdi mdi-check text-success me-1"></i> تفعيل/إلغاء تفعيل الماسحين</li>
                <li><i class="mdi mdi-check text-success me-1"></i> متابعة نشاط الماسحين</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Scanner Details Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل الماسح الضوئي</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="scannerModalBody">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

<!-- Scanner Activity Modal -->
<div class="modal fade" id="activityModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">نشاط الماسح الضوئي</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="activityModalBody">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

<script>
function viewScanner(scannerId) {
  fetch(`/venue-manager/staff/${scannerId}`)
    .then(response => response.json())
    .then(data => {
      document.getElementById('scannerModalBody').innerHTML = `
        <div class="row">
          <div class="col-12">
            <p><strong>الاسم الكامل:</strong> ${data.fullName}</p>
            <p><strong>البريد الإلكتروني:</strong> ${data.email}</p>
            <p><strong>رقم الهاتف:</strong> ${data.phone || 'غير محدد'}</p>
            <p><strong>تاريخ التعيين:</strong> ${new Date(data.createdAt).toLocaleDateString('ar-SA')}</p>
            <p><strong>الحالة:</strong> ${data.isActive ? 'نشط' : 'غير نشط'}</p>
            <p><strong>آخر تحديث:</strong> ${new Date(data.updatedAt).toLocaleDateString('ar-SA')}</p>
          </div>
        </div>
      `;
      new bootstrap.Modal(document.getElementById('scannerModal')).show();
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل تفاصيل الماسح الضوئي');
    });
}

function viewScannerActivity(scannerId) {
  fetch(`/venue-manager/staff/${scannerId}/activity`)
    .then(response => response.json())
    .then(data => {
      let activityHtml = `
        <div class="row mb-3">
          <div class="col-md-4">
            <div class="card bg-primary text-white">
              <div class="card-body text-center">
                <h3>${data.stats.totalScans}</h3>
                <p class="mb-0">إجمالي المسح</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h3>${data.stats.todayScans}</h3>
                <p class="mb-0">مسح اليوم</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card bg-info text-white">
              <div class="card-body text-center">
                <h3>${data.stats.thisWeekScans}</h3>
                <p class="mb-0">مسح هذا الأسبوع</p>
              </div>
            </div>
          </div>
        </div>
      `;

      if (data.recentActivity.length > 0) {
        activityHtml += `
          <h6>آخر النشاطات</h6>
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>الحفلة</th>
                  <th>اسم الضيف</th>
                  <th>وقت المسح</th>
                </tr>
              </thead>
              <tbody>
        `;
        
        data.recentActivity.forEach(activity => {
          activityHtml += `
            <tr>
              <td>${activity.party.title}</td>
              <td>${activity.guestName}</td>
              <td>${new Date(activity.attendedAt).toLocaleString('ar-SA')}</td>
            </tr>
          `;
        });
        
        activityHtml += `
              </tbody>
            </table>
          </div>
        `;
      } else {
        activityHtml += '<p class="text-center text-muted">لا يوجد نشاط حديث</p>';
      }

      document.getElementById('activityModalBody').innerHTML = activityHtml;
      new bootstrap.Modal(document.getElementById('activityModal')).show();
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل نشاط الماسح الضوئي');
    });
}

function activateScanner(scannerId) {
  if (confirm('هل أنت متأكد من تفعيل هذا الماسح الضوئي؟')) {
    updateScannerStatus(scannerId, true);
  }
}

function deactivateScanner(scannerId) {
  if (confirm('هل أنت متأكد من إلغاء تفعيل هذا الماسح الضوئي؟')) {
    updateScannerStatus(scannerId, false);
  }
}

function updateScannerStatus(scannerId, isActive) {
  fetch(`/venue-manager/staff/${scannerId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ isActive })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert(data.error || 'حدث خطأ');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في تحديث حالة الماسح الضوئي');
  });
}

function unassignScanner(scannerId) {
  if (confirm('هل أنت متأكد من إلغاء تعيين هذا الماسح الضوئي؟ سيتم إزالته من القاعة ولن يتمكن من الوصول إليها.')) {
    fetch(`/venue-manager/staff/${scannerId}/unassign`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert(data.error || 'حدث خطأ');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في إلغاء تعيين الماسح الضوئي');
    });
  }
}
</script>
