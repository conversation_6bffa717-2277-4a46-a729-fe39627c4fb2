const express = require('express');
const session = require('express-session');
const SequelizeStore = require('connect-session-sequelize')(session.Store);
const path = require('path');
const helmet = require('helmet');
const cors = require('cors');
require('dotenv').config();

// Import database connection
const sequelize = require('./config/database');

// Import routes
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');
const hallManagerRoutes = require('./routes/hallManager');
const partyOwnerRoutes = require('./routes/partyOwner');
const scannerRoutes = require('./routes/scanner');

// Import middleware
const authMiddleware = require('./middleware/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(cors());

// View engine setup
const exphbs = require('express-handlebars');
const hbs = exphbs.create({
  defaultLayout: 'main',
  layoutsDir: path.join(__dirname, 'views/layouts'),
  partialsDir: path.join(__dirname, 'views/partials'),
  helpers: {
    eq: (a, b) => a === b,
    ne: (a, b) => a !== b,
    gt: (a, b) => a > b,
    lt: (a, b) => a < b,
    formatDate: (date) => {
      if (!date) return '';
      return new Date(date).toLocaleDateString('ar-SA');
    },
    formatTime: (time) => {
      if (!time) return '';
      return time.substring(0, 5);
    },
    formatDateTime: (datetime) => {
      if (!datetime) return '';
      return new Date(datetime).toLocaleString('ar-SA');
    },
    json: (context) => JSON.stringify(context),
    statusBadge: (status) => {
      const badges = {
        planned: 'badge-primary',
        active: 'badge-success',
        completed: 'badge-secondary',
        cancelled: 'badge-danger',
        pending: 'badge-warning',
        sent: 'badge-info',
        attended: 'badge-success',
        no_show: 'badge-danger'
      };
      return badges[status] || 'badge-secondary';
    },
    statusText: (status) => {
      const texts = {
        planned: 'مجدولة',
        active: 'نشطة',
        completed: 'مكتملة',
        cancelled: 'ملغية',
        pending: 'في الانتظار',
        sent: 'مرسلة',
        attended: 'حضر',
        no_show: 'لم يحضر'
      };
      return texts[status] || status;
    }
  }
});

app.engine('handlebars', hbs.engine);
app.set('view engine', 'handlebars');
app.set('views', path.join(__dirname, 'views'));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Session configuration
const sessionStore = new SequelizeStore({
  db: sequelize,
});

app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  store: sessionStore,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to true in production with HTTPS
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Create session table
sessionStore.sync();

// Make user available in all views
app.use((req, res, next) => {
  res.locals.user = req.session.user || null;
  res.locals.isAuthenticated = !!req.session.user;
  next();
});

// Routes
app.use('/auth', authRoutes);
app.use('/admin', authMiddleware.requireAuth, authMiddleware.requireRole(['admin']), adminRoutes);
app.use('/hall-manager', authMiddleware.requireAuth, authMiddleware.requireRole(['hall_manager']), hallManagerRoutes);
app.use('/party-owner', authMiddleware.requireAuth, authMiddleware.requireRole(['party_owner']), partyOwnerRoutes);
app.use('/scanner', authMiddleware.requireAuth, authMiddleware.requireRole(['scanner']), scannerRoutes);

// Home route
app.get('/', (req, res) => {
  if (req.session.user) {
    // Redirect based on user role
    switch (req.session.user.role) {
      case 'admin':
        return res.redirect('/admin/dashboard');
      case 'hall_manager':
        return res.redirect('/hall-manager/dashboard');
      case 'party_owner':
        return res.redirect('/party-owner/dashboard');
      case 'scanner':
        return res.redirect('/scanner/dashboard');
      default:
        return res.redirect('/auth/login');
    }
  }
  res.redirect('/auth/login');
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render('error', {
    title: 'خطأ في الخادم',
    message: 'حدث خطأ في الخادم',
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).render('error', {
    title: 'الصفحة غير موجودة',
    message: 'الصفحة المطلوبة غير موجودة'
  });
});

// Database connection and server start
async function startServer() {
  try {
    await sequelize.authenticate();
    console.log('تم الاتصال بقاعدة البيانات بنجاح');
    
    // Check database connection only (no sync)
    console.log('قاعدة البيانات متصلة ومستعدة');
    
    app.listen(PORT, () => {
      console.log(`الخادم يعمل على المنفذ ${PORT}`);
      console.log(`http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('خطأ في بدء الخادم:', error);
    process.exit(1);
  }
}

startServer();
