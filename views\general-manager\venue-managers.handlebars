<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إدارة مديري القاعات</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/general-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">إدارة مديري القاعات</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  {{#if success}}
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    {{success}}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  {{/if}}

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-4">
              <a href="/general-manager/venue-managers/create" class="btn btn-primary mb-2">
                <i class="mdi mdi-account-plus me-1"></i>
                إنشاء مدير قاعة جديد
              </a>
            </div>
            <div class="col-sm-8">
              <div class="text-sm-end">
                <form class="d-flex" method="GET">
                  <input type="text" class="form-control me-2" name="search" placeholder="البحث في المديرين..." value="{{search}}">
                  <button class="btn btn-outline-secondary" type="submit">بحث</button>
                </form>
              </div>
            </div>
          </div>

          <div class="table-responsive">
            <table class="table table-centered table-striped dt-responsive nowrap w-100">
              <thead>
                <tr>
                  <th>الاسم الكامل</th>
                  <th>البريد الإلكتروني</th>
                  <th>رقم الهاتف</th>
                  <th>القاعة المُدارة</th>
                  <th>تاريخ الإنشاء</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {{#each venueManagers}}
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-primary d-flex align-items-center justify-content-center">
                          <span class="text-white">{{substring fullName 0 1}}</span>
                        </div>
                      </div>
                      <div class="flex-grow-1 ms-2">
                        <h5 class="my-0">{{fullName}}</h5>
                      </div>
                    </div>
                  </td>
                  <td>{{email}}</td>
                  <td>{{#if phone}}{{phone}}{{else}}-{{/if}}</td>
                  <td>
                    {{#if managedVenue}}
                    <span class="badge bg-success">{{managedVenue.name}}</span>
                    {{else}}
                    <span class="badge bg-warning">غير مُعيَّن</span>
                    {{/if}}
                  </td>
                  <td>{{formatDate createdAt}}</td>
                  <td>
                    {{#if isActive}}
                    <span class="badge bg-success">نشط</span>
                    {{else}}
                    <span class="badge bg-danger">غير نشط</span>
                    {{/if}}
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewManager('{{id}}')">
                        <i class="mdi mdi-eye"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-warning" onclick="editManager('{{id}}')">
                        <i class="mdi mdi-pencil"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-info" onclick="assignVenue('{{id}}')">
                        <i class="mdi mdi-home-plus"></i>
                      </button>
                      {{#unless isActive}}
                      <button type="button" class="btn btn-sm btn-outline-success" onclick="activateManager('{{id}}')">
                        <i class="mdi mdi-check"></i>
                      </button>
                      {{else}}
                      <button type="button" class="btn btn-sm btn-outline-danger" onclick="deactivateManager('{{id}}')">
                        <i class="mdi mdi-close"></i>
                      </button>
                      {{/unless}}
                    </div>
                  </td>
                </tr>
                {{else}}
                <tr>
                  <td colspan="7" class="text-center">لا يوجد مديري قاعات</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>

          {{#if pagination}}
          <div class="row">
            <div class="col-12">
              <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                  {{#if pagination.hasPrev}}
                  <li class="page-item">
                    <a class="page-link" href="?page={{subtract pagination.currentPage 1}}">السابق</a>
                  </li>
                  {{/if}}
                  
                  <li class="page-item active">
                    <span class="page-link">{{pagination.currentPage}} من {{pagination.totalPages}}</span>
                  </li>
                  
                  {{#if pagination.hasNext}}
                  <li class="page-item">
                    <a class="page-link" href="?page={{add pagination.currentPage 1}}">التالي</a>
                  </li>
                  {{/if}}
                </ul>
              </nav>
            </div>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Manager Details Modal -->
<div class="modal fade" id="managerModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل مدير القاعة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="managerModalBody">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

<!-- Assign Venue Modal -->
<div class="modal fade" id="assignVenueModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تعيين قاعة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="assignVenueForm">
          <input type="hidden" id="managerId" name="managerId">
          <div class="mb-3">
            <label for="venueId" class="form-label">اختر القاعة</label>
            <select class="form-select" id="venueId" name="venueId" required>
              <option value="">اختر قاعة...</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="submitAssignVenue()">تعيين</button>
      </div>
    </div>
  </div>
</div>

<script>
function viewManager(managerId) {
  fetch(`/general-manager/venue-managers/${managerId}`)
    .then(response => response.json())
    .then(data => {
      document.getElementById('managerModalBody').innerHTML = `
        <div class="row">
          <div class="col-12">
            <p><strong>الاسم الكامل:</strong> ${data.fullName}</p>
            <p><strong>البريد الإلكتروني:</strong> ${data.email}</p>
            <p><strong>رقم الهاتف:</strong> ${data.phone || 'غير محدد'}</p>
            <p><strong>القاعة المُدارة:</strong> ${data.managedVenue ? data.managedVenue.name : 'غير مُعيَّن'}</p>
            <p><strong>تاريخ الإنشاء:</strong> ${new Date(data.createdAt).toLocaleDateString('ar-SA')}</p>
            <p><strong>الحالة:</strong> ${data.isActive ? 'نشط' : 'غير نشط'}</p>
          </div>
        </div>
      `;
      new bootstrap.Modal(document.getElementById('managerModal')).show();
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل تفاصيل المدير');
    });
}

function editManager(managerId) {
  window.location.href = `/general-manager/venue-managers/${managerId}/edit`;
}

function assignVenue(managerId) {
  document.getElementById('managerId').value = managerId;
  
  // Load available venues
  fetch('/general-manager/venues/available')
    .then(response => response.json())
    .then(venues => {
      const select = document.getElementById('venueId');
      select.innerHTML = '<option value="">اختر قاعة...</option>';
      venues.forEach(venue => {
        select.innerHTML += `<option value="${venue.id}">${venue.name}</option>`;
      });
      new bootstrap.Modal(document.getElementById('assignVenueModal')).show();
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل القاعات المتاحة');
    });
}

function submitAssignVenue() {
  const form = document.getElementById('assignVenueForm');
  const formData = new FormData(form);
  
  fetch('/general-manager/venue-managers/assign-venue', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert(data.error || 'حدث خطأ');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في تعيين القاعة');
  });
}

function activateManager(managerId) {
  if (confirm('هل أنت متأكد من تفعيل هذا المدير؟')) {
    updateManagerStatus(managerId, true);
  }
}

function deactivateManager(managerId) {
  if (confirm('هل أنت متأكد من إلغاء تفعيل هذا المدير؟')) {
    updateManagerStatus(managerId, false);
  }
}

function updateManagerStatus(managerId, isActive) {
  fetch(`/general-manager/venue-managers/${managerId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ isActive })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert(data.error || 'حدث خطأ');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في تحديث حالة المدير');
  });
}
</script>
