const fs = require('fs');
const path = require('path');
const sequelize = require('../config/database');

async function runMigrations() {
  console.log('🔄 بدء تشغيل migrations...');

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // Get all migration files
    const migrationsPath = path.join(__dirname, '..', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsPath)
      .filter(file => file.endsWith('.js'))
      .sort();

    console.log(`📁 تم العثور على ${migrationFiles.length} migration files`);

    // Run each migration
    for (const file of migrationFiles) {
      console.log(`🔄 تشغيل migration: ${file}`);

      const migrationPath = path.join(migrationsPath, file);
      const migration = require(migrationPath);

      if (migration.up) {
        await migration.up(sequelize.getQueryInterface(), sequelize.constructor);
        console.log(`✅ تم تشغيل ${file} بنجاح`);
      } else {
        console.warn(`⚠️  لا يحتوي ${file} على دالة up`);
      }
    }

    console.log('✅ تم تشغيل جميع migrations بنجاح!');

  } catch (error) {
    console.error('❌ فشل في تشغيل migrations:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  runMigrations().then(() => {
    console.log('🎉 انتهت عملية Migration بنجاح!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ فشلت عملية Migration:', error);
    process.exit(1);
  });
}

module.exports = { runMigrations };
