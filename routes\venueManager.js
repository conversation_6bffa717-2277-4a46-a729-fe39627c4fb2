const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, Venue, Party, Invitation } = require('../models');
const { Op } = require('sequelize');

const router = express.Router();

// Venue Manager Dashboard
router.get('/dashboard', async (req, res) => {
  try {
    const userId = req.session.user.id;

    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      return res.render('error', {
        title: 'خطأ',
        message: 'لم يتم تعيين قاعة لك بعد. يرجى التواصل مع المدير العام.'
      });
    }

    // Get statistics
    const [totalParties, activeParties, todayParties, totalInvitations] = await Promise.all([
      Party.count({ where: { venueId: managedVenue.id } }),
      Party.count({ where: { venueId: managedVenue.id, status: 'active' } }),
      Party.count({ 
        where: { 
          venueId: managedVenue.id,
          partyDate: new Date().toISOString().split('T')[0]
        }
      }),
      Invitation.count({
        include: [
          { model: Party, as: 'party', where: { venueId: managedVenue.id } }
        ]
      })
    ]);

    // Get recent parties
    const recentParties = await Party.findAll({
      where: { venueId: managedVenue.id },
      include: [
        { model: User, as: 'owner', attributes: ['fullName'] }
      ],
      order: [['partyDate', 'DESC']],
      limit: 5
    });

    res.render('venue-manager/dashboard', {
      title: 'لوحة تحكم مدير القاعة',
      stats: {
        totalParties,
        activeParties,
        todayParties,
        totalInvitations
      },
      managedVenue,
      recentParties
    });
  } catch (error) {
    console.error('Venue manager dashboard error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل لوحة التحكم'
    });
  }
});

// Parties Management
router.get('/parties', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;

    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      return res.render('error', {
        title: 'خطأ',
        message: 'لم يتم تعيين قاعة لك بعد'
      });
    }

    const { count, rows: parties } = await Party.findAndCountAll({
      where: { venueId: managedVenue.id },
      include: [
        { model: User, as: 'owner', attributes: ['fullName', 'email'] }
      ],
      order: [['partyDate', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('venue-manager/parties', {
      title: 'إدارة الحفلات',
      parties,
      managedVenue,
      pagination: {
        currentPage: page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Parties list error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل قائمة الحفلات'
    });
  }
});

// Create Party Page
router.get('/parties/create', async (req, res) => {
  try {
    const userId = req.session.user.id;
    
    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      return res.render('error', {
        title: 'خطأ',
        message: 'لم يتم تعيين قاعة لك بعد'
      });
    }

    // Get party owners
    const partyOwners = await User.findByRole('party_owner');

    res.render('venue-manager/create-party', {
      title: 'إنشاء حفلة جديدة',
      managedVenue,
      partyOwners
    });
  } catch (error) {
    console.error('Create party page error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل صفحة إنشاء الحفلة'
    });
  }
});

// Create Party Process
router.post('/parties/create', [
  body('title').isLength({ min: 2 }).withMessage('عنوان الحفلة مطلوب'),
  body('ownerId').isUUID().withMessage('صاحب الحفلة مطلوب'),
  body('partyDate').isDate().withMessage('تاريخ الحفلة غير صحيح'),
  body('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('وقت البداية غير صحيح'),
  body('endTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('وقت النهاية غير صحيح'),
  body('expectedGuests').isInt({ min: 1 }).withMessage('عدد الضيوف المتوقع يجب أن يكون رقم صحيح أكبر من 0')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const userId = req.session.user.id;
      const managedVenue = await Venue.findOne({
        include: [
          { model: User, as: 'manager', where: { id: userId } }
        ]
      });
      const partyOwners = await User.findByRole('party_owner');
      
      return res.render('venue-manager/create-party', {
        title: 'إنشاء حفلة جديدة',
        managedVenue,
        partyOwners,
        errors: errors.array(),
        formData: req.body
      });
    }

    const { title, description, type, ownerId, partyDate, startTime, endTime, expectedGuests, totalCost, notes } = req.body;
    const userId = req.session.user.id;

    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      throw new Error('لم يتم تعيين قاعة لك بعد');
    }

    // Check venue availability
    const isAvailable = await managedVenue.isAvailable(partyDate, startTime, endTime);
    if (!isAvailable) {
      throw new Error('القاعة غير متاحة في هذا الوقت');
    }

    await Party.create({
      title,
      description: description || null,
      type: type || 'other',
      venueId: managedVenue.id,
      ownerId,
      partyDate,
      startTime,
      endTime,
      expectedGuests: parseInt(expectedGuests),
      totalCost: totalCost ? parseFloat(totalCost) : null,
      notes: notes || null
    });

    res.redirect('/venue-manager/parties?success=تم إنشاء الحفلة بنجاح');
  } catch (error) {
    console.error('Create party error:', error);
    const userId = req.session.user.id;
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });
    const partyOwners = await User.findByRole('party_owner');
    
    res.render('venue-manager/create-party', {
      title: 'إنشاء حفلة جديدة',
      managedVenue,
      partyOwners,
      error: error.message || 'حدث خطأ أثناء إنشاء الحفلة',
      formData: req.body
    });
  }
});

// Staff Management (Scanners)
router.get('/staff', async (req, res) => {
  try {
    const userId = req.session.user.id;

    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      return res.render('error', {
        title: 'خطأ',
        message: 'لم يتم تعيين قاعة لك بعد'
      });
    }

    // Get assigned scanners
    const assignedScanners = await User.findAll({
      where: { 
        role: 'scanner',
        managedVenueId: managedVenue.id,
        isActive: true
      },
      order: [['fullName', 'ASC']]
    });

    res.render('venue-manager/staff', {
      title: 'إدارة الموظفين',
      assignedScanners,
      managedVenue
    });
  } catch (error) {
    console.error('Staff list error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل قائمة الموظفين'
    });
  }
});

// Assign Scanner Page
router.get('/staff/assign', async (req, res) => {
  try {
    const userId = req.session.user.id;

    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      return res.render('error', {
        title: 'خطأ',
        message: 'لم يتم تعيين قاعة لك بعد'
      });
    }

    // Get available scanners (not assigned to any venue)
    const availableScanners = await User.findAll({
      where: { 
        role: 'scanner',
        managedVenueId: null,
        isActive: true
      },
      order: [['fullName', 'ASC']]
    });

    res.render('venue-manager/assign-staff', {
      title: 'تعيين ماسح ضوئي',
      managedVenue,
      availableScanners
    });
  } catch (error) {
    console.error('Assign staff page error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل صفحة تعيين الموظف'
    });
  }
});

// Assign Scanner Process
router.post('/staff/assign', [
  body('scannerId').isUUID().withMessage('الماسح الضوئي مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const userId = req.session.user.id;
      const managedVenue = await Venue.findOne({
        include: [
          { model: User, as: 'manager', where: { id: userId } }
        ]
      });
      const availableScanners = await User.findAll({
        where: { 
          role: 'scanner',
          managedVenueId: null,
          isActive: true
        },
        order: [['fullName', 'ASC']]
      });
      
      return res.render('venue-manager/assign-staff', {
        title: 'تعيين ماسح ضوئي',
        managedVenue,
        availableScanners,
        errors: errors.array(),
        formData: req.body
      });
    }

    const { scannerId } = req.body;
    const userId = req.session.user.id;

    // Get managed venue
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });

    if (!managedVenue) {
      throw new Error('لم يتم تعيين قاعة لك بعد');
    }

    // Assign scanner to venue
    await User.update(
      { managedVenueId: managedVenue.id },
      { where: { id: scannerId, role: 'scanner' } }
    );

    res.redirect('/venue-manager/staff?success=تم تعيين الماسح الضوئي بنجاح');
  } catch (error) {
    console.error('Assign scanner error:', error);
    const userId = req.session.user.id;
    const managedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { id: userId } }
      ]
    });
    const availableScanners = await User.findAll({
      where: { 
        role: 'scanner',
        managedVenueId: null,
        isActive: true
      },
      order: [['fullName', 'ASC']]
    });
    
    res.render('venue-manager/assign-staff', {
      title: 'تعيين ماسح ضوئي',
      managedVenue,
      availableScanners,
      error: error.message || 'حدث خطأ أثناء تعيين الماسح الضوئي',
      formData: req.body
    });
  }
});

module.exports = router;
