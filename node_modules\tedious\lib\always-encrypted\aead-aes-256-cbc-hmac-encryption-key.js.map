{"version": 3, "file": "aead-aes-256-cbc-hmac-encryption-key.js", "names": ["_crypto", "require", "_<PERSON><PERSON><PERSON>", "_interopRequireDefault", "obj", "__esModule", "default", "keySize", "exports", "keySizeInBytes", "<PERSON><PERSON><PERSON>", "root<PERSON>ey", "salt", "hmac", "createHmac", "update", "<PERSON><PERSON><PERSON>", "from", "digest", "generateKeySalt", "keyType", "algorithmName", "AeadAes256CbcHmac256EncryptionKey", "SymmetricKey", "constructor", "encryptionKeySaltFormat", "macKeySaltFormat", "ivKeySaltFormat", "length", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "iv<PERSON><PERSON>", "error", "message", "getEncryptionKey", "getMacKey", "getIvKey"], "sources": ["../../src/always-encrypted/aead-aes-256-cbc-hmac-encryption-key.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { createHmac } from 'crypto';\nimport SymmetricKey from './symmetric-key';\n\nexport const keySize = 256;\nconst keySizeInBytes = keySize / 8;\n\nexport const deriveKey = (rootKey: Buffer, salt: string): Buffer => {\n  const hmac = createHmac('sha256', rootKey);\n  hmac.update(Buffer.from(salt, 'utf16le'));\n  return hmac.digest();\n};\n\nexport const generateKeySalt = (\n  keyType: 'encryption' | 'MAC' | 'IV',\n  algorithmName: string,\n  keySize: number,\n): string =>\n  `Microsoft SQL Server cell ${keyType} key ` +\n  `with encryption algorithm:${algorithmName} and key length:${keySize}`;\n\nexport class AeadAes256CbcHmac256EncryptionKey extends SymmetricKey {\n  declare private readonly algorithmName: string;\n  declare private encryptionKeySaltFormat: string;\n  declare private macKeySaltFormat: string;\n  declare private ivKeySaltFormat: string;\n  declare private encryptionKey: SymmetricKey;\n  declare private macKey: SymmetricKey;\n  declare private ivKey: SymmetricKey;\n\n  constructor(rootKey: Buffer, algorithmName: string) {\n    super(rootKey);\n    this.algorithmName = algorithmName;\n    this.encryptionKeySaltFormat = generateKeySalt('encryption', this.algorithmName, keySize);\n    this.macKeySaltFormat = generateKeySalt('MAC', this.algorithmName, keySize);\n    this.ivKeySaltFormat = generateKeySalt('IV', this.algorithmName, keySize);\n\n    if (rootKey.length !== keySizeInBytes) {\n      throw new Error(`The column encryption key has been successfully decrypted but it's length: ${rootKey.length} does not match the length: ${keySizeInBytes} for algorithm \"${this.algorithmName}\". Verify the encrypted value of the column encryption key in the database.`);\n    }\n\n    try {\n      const encKeyBuff = deriveKey(rootKey, this.encryptionKeySaltFormat);\n\n      this.encryptionKey = new SymmetricKey(encKeyBuff);\n\n      const macKeyBuff = deriveKey(rootKey, this.macKeySaltFormat);\n\n      this.macKey = new SymmetricKey(macKeyBuff);\n\n      const ivKeyBuff = deriveKey(rootKey, this.ivKeySaltFormat);\n\n      this.ivKey = new SymmetricKey(ivKeyBuff);\n    } catch (error: any) {\n      throw new Error(`Key extraction failed : ${error.message}.`);\n    }\n  }\n\n  getEncryptionKey(): Buffer {\n    return this.encryptionKey.rootKey;\n  }\n\n  getMacKey(): Buffer {\n    return this.macKey.rootKey;\n  }\n\n  getIvKey(): Buffer {\n    return this.ivKey.rootKey;\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA2C,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAJ3C;AACA;;AAKO,MAAMG,OAAO,GAAG,GAAG;AAACC,OAAA,CAAAD,OAAA,GAAAA,OAAA;AAC3B,MAAME,cAAc,GAAGF,OAAO,GAAG,CAAC;AAE3B,MAAMG,SAAS,GAAGA,CAACC,OAAe,EAAEC,IAAY,KAAa;EAClE,MAAMC,IAAI,GAAG,IAAAC,kBAAU,EAAC,QAAQ,EAAEH,OAAO,CAAC;EAC1CE,IAAI,CAACE,MAAM,CAACC,MAAM,CAACC,IAAI,CAACL,IAAI,EAAE,SAAS,CAAC,CAAC;EACzC,OAAOC,IAAI,CAACK,MAAM,CAAC,CAAC;AACtB,CAAC;AAACV,OAAA,CAAAE,SAAA,GAAAA,SAAA;AAEK,MAAMS,eAAe,GAAGA,CAC7BC,OAAoC,EACpCC,aAAqB,EACrBd,OAAe,KAEd,6BAA4Ba,OAAQ,OAAM,GAC1C,6BAA4BC,aAAc,mBAAkBd,OAAQ,EAAC;AAACC,OAAA,CAAAW,eAAA,GAAAA,eAAA;AAElE,MAAMG,iCAAiC,SAASC,qBAAY,CAAC;EASlEC,WAAWA,CAACb,OAAe,EAAEU,aAAqB,EAAE;IAClD,KAAK,CAACV,OAAO,CAAC;IACd,IAAI,CAACU,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACI,uBAAuB,GAAGN,eAAe,CAAC,YAAY,EAAE,IAAI,CAACE,aAAa,EAAEd,OAAO,CAAC;IACzF,IAAI,CAACmB,gBAAgB,GAAGP,eAAe,CAAC,KAAK,EAAE,IAAI,CAACE,aAAa,EAAEd,OAAO,CAAC;IAC3E,IAAI,CAACoB,eAAe,GAAGR,eAAe,CAAC,IAAI,EAAE,IAAI,CAACE,aAAa,EAAEd,OAAO,CAAC;IAEzE,IAAII,OAAO,CAACiB,MAAM,KAAKnB,cAAc,EAAE;MACrC,MAAM,IAAIoB,KAAK,CAAE,8EAA6ElB,OAAO,CAACiB,MAAO,+BAA8BnB,cAAe,mBAAkB,IAAI,CAACY,aAAc,6EAA4E,CAAC;IAC9Q;IAEA,IAAI;MACF,MAAMS,UAAU,GAAGpB,SAAS,CAACC,OAAO,EAAE,IAAI,CAACc,uBAAuB,CAAC;MAEnE,IAAI,CAACM,aAAa,GAAG,IAAIR,qBAAY,CAACO,UAAU,CAAC;MAEjD,MAAME,UAAU,GAAGtB,SAAS,CAACC,OAAO,EAAE,IAAI,CAACe,gBAAgB,CAAC;MAE5D,IAAI,CAACO,MAAM,GAAG,IAAIV,qBAAY,CAACS,UAAU,CAAC;MAE1C,MAAME,SAAS,GAAGxB,SAAS,CAACC,OAAO,EAAE,IAAI,CAACgB,eAAe,CAAC;MAE1D,IAAI,CAACQ,KAAK,GAAG,IAAIZ,qBAAY,CAACW,SAAS,CAAC;IAC1C,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnB,MAAM,IAAIP,KAAK,CAAE,2BAA0BO,KAAK,CAACC,OAAQ,GAAE,CAAC;IAC9D;EACF;EAEAC,gBAAgBA,CAAA,EAAW;IACzB,OAAO,IAAI,CAACP,aAAa,CAACpB,OAAO;EACnC;EAEA4B,SAASA,CAAA,EAAW;IAClB,OAAO,IAAI,CAACN,MAAM,CAACtB,OAAO;EAC5B;EAEA6B,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACL,KAAK,CAACxB,OAAO;EAC3B;AACF;AAACH,OAAA,CAAAc,iCAAA,GAAAA,iCAAA"}