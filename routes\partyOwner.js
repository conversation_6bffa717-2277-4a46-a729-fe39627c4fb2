const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, Venue, Party, Invitation } = require('../models');
const { Op } = require('sequelize');
const QRCode = require('qrcode');
const { Op } = require('sequelize');

const router = express.Router();

// Party Owner Dashboard
router.get('/dashboard', async (req, res) => {
  try {
    const userId = req.session.user.id;

    // Get user's parties
    const parties = await Party.findAll({
      where: { ownerId: userId },
      include: [
        { model: Venue, as: 'venue', attributes: ['name', 'location'] }
      ],
      order: [['partyDate', 'DESC']]
    });

    // Get statistics
    const [totalParties, activeParties, totalInvitations, attendedGuests] = await Promise.all([
      Party.count({ where: { ownerId: userId } }),
      Party.count({ where: { ownerId: userId, status: 'active' } }),
      Invitation.count({
        include: [
          { model: Party, as: 'party', where: { ownerId: userId } }
        ]
      }),
      Invitation.count({
        where: { status: 'attended' },
        include: [
          { model: Party, as: 'party', where: { ownerId: userId } }
        ]
      })
    ]);

    res.render('party-owner/dashboard', {
      title: 'لوحة تحكم صاحب الحفلة',
      stats: {
        totalParties,
        activeParties,
        totalInvitations,
        attendedGuests
      },
      parties
    });
  } catch (error) {
    console.error('Party owner dashboard error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل لوحة التحكم'
    });
  }
});

// Party Details
router.get('/parties/:id', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const partyId = req.params.id;

    const party = await Party.findOne({
      where: { id: partyId, ownerId: userId },
      include: [
        { model: Venue, as: 'venue' },
        {
          model: Invitation,
          as: 'invitations'
        }
      ]
    });

    if (!party) {
      return res.status(404).render('error', {
        title: 'الحفلة غير موجودة',
        message: 'الحفلة المطلوبة غير موجودة'
      });
    }

    // Calculate statistics
    const totalInvitations = party.invitations.length;
    const sentInvitations = party.invitations.filter(inv => ['sent', 'confirmed', 'attended'].includes(inv.status)).length;
    const attendedGuests = party.invitations.filter(inv => inv.status === 'attended').length;

    res.render('party-owner/party-details', {
      title: party.title,
      party,
      stats: {
        totalInvitations,
        sentInvitations,
        attendedGuests
      }
    });
  } catch (error) {
    console.error('Party details error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل تفاصيل الحفلة'
    });
  }
});

// Add Guest Page
router.get('/parties/:id/guests/add', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const partyId = req.params.id;

    const party = await Party.findOne({
      where: { id: partyId, ownerId: userId },
      include: [{ model: Venue, as: 'venue' }]
    });

    if (!party) {
      return res.status(404).render('error', {
        title: 'الحفلة غير موجودة',
        message: 'الحفلة المطلوبة غير موجودة'
      });
    }

    res.render('party-owner/add-guest', {
      title: 'إضافة مدعو',
      party
    });
  } catch (error) {
    console.error('Add guest page error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل صفحة إضافة المدعو'
    });
  }
});

// Add Guest Process (Create Invitation)
router.post('/parties/:id/guests/add', [
  body('guestName').isLength({ min: 2 }).withMessage('اسم المدعو مطلوب'),
  body('guestPhone').optional().isMobilePhone('ar-SA').withMessage('رقم الهاتف غير صحيح'),
  body('guestEmail').optional().isEmail().withMessage('البريد الإلكتروني غير صحيح')
], async (req, res) => {
  try {
    const userId = req.session.user.id;
    const partyId = req.params.id;

    // Verify party ownership
    const party = await Party.findOne({
      where: { id: partyId, ownerId: userId },
      include: [{ model: Venue, as: 'venue' }]
    });

    if (!party) {
      return res.status(404).render('error', {
        title: 'الحفلة غير موجودة',
        message: 'الحفلة المطلوبة غير موجودة'
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('party-owner/add-guest', {
        title: 'إضافة مدعو',
        party,
        errors: errors.array(),
        formData: req.body
      });
    }

    const { guestName, guestPhone, guestEmail, notes } = req.body;

    // Check if invitation already exists for this guest
    const existingInvitation = await Invitation.findOne({
      where: {
        partyId,
        [Op.or]: [
          { guestPhone: guestPhone },
          { guestEmail: guestEmail }
        ]
      }
    });

    if (existingInvitation) {
      return res.render('party-owner/add-guest', {
        title: 'إضافة مدعو',
        party,
        error: 'دعوة لهذا الضيف موجودة بالفعل',
        formData: req.body
      });
    }

    // Generate QR code
    const qrCode = `INV-${partyId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

    await Invitation.create({
      partyId,
      guestName,
      guestPhone: guestPhone || null,
      guestEmail: guestEmail || null,
      qrCode,
      notes: notes || null
    });

    res.redirect(`/party-owner/parties/${partyId}?success=تم إضافة المدعو بنجاح`);
  } catch (error) {
    console.error('Add guest error:', error);
    const party = await Party.findByPk(req.params.id, {
      include: [{ model: Venue, as: 'venue' }]
    });
    
    res.render('party-owner/add-guest', {
      title: 'إضافة مدعو',
      party,
      error: 'حدث خطأ أثناء إضافة المدعو',
      formData: req.body
    });
  }
});

// Generate Invitations
router.post('/parties/:id/generate-invitations', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const partyId = req.params.id;

    // Verify party ownership
    const party = await Party.findOne({
      where: { id: partyId, ownerId: userId },
      include: [
        { model: Hall, as: 'Hall' },
        { 
          model: Guest, 
          as: 'Guests',
          include: [{ model: Invitation, as: 'Invitation' }]
        }
      ]
    });

    if (!party) {
      return res.status(404).json({ error: 'الحفلة غير موجودة' });
    }

    // Get guests without invitations
    const guestsWithoutInvitations = party.Guests.filter(guest => !guest.Invitation);

    if (guestsWithoutInvitations.length === 0) {
      return res.json({ 
        success: false, 
        message: 'جميع المدعوين لديهم دعوات بالفعل' 
      });
    }

    // Generate invitations
    const invitations = [];
    for (const guest of guestsWithoutInvitations) {
      const qrCode = `${partyId}-${guest.id}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      
      invitations.push({
        guestId: guest.id,
        qrCode,
        status: 'pending'
      });
    }

    await Invitation.bulkCreate(invitations);

    res.json({ 
      success: true, 
      message: `تم إنشاء ${invitations.length} دعوة جديدة`,
      count: invitations.length
    });
  } catch (error) {
    console.error('Generate invitations error:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء إنشاء الدعوات' });
  }
});

// View Invitation QR Code
router.get('/invitations/:id/qr', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const invitationId = req.params.id;

    const invitation = await Invitation.findOne({
      where: { id: invitationId },
      include: [
        {
          model: Guest,
          as: 'Guest',
          include: [
            {
              model: Party,
              as: 'Party',
              where: { ownerId: userId },
              include: [{ model: Hall, as: 'Hall' }]
            }
          ]
        }
      ]
    });

    if (!invitation) {
      return res.status(404).render('error', {
        title: 'الدعوة غير موجودة',
        message: 'الدعوة المطلوبة غير موجودة'
      });
    }

    // Generate QR code
    const qrCodeDataURL = await QRCode.toDataURL(invitation.qrCode, {
      width: 300,
      margin: 2
    });

    res.render('party-owner/invitation-qr', {
      title: `دعوة ${invitation.Guest.name}`,
      invitation,
      qrCodeDataURL
    });
  } catch (error) {
    console.error('View invitation QR error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل رمز QR للدعوة'
    });
  }
});

// Download Invitation QR Code
router.get('/invitations/:id/download', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const invitationId = req.params.id;

    const invitation = await Invitation.findOne({
      where: { id: invitationId },
      include: [
        {
          model: Guest,
          as: 'Guest',
          include: [
            {
              model: Party,
              as: 'Party',
              where: { ownerId: userId },
              include: [{ model: Hall, as: 'Hall' }]
            }
          ]
        }
      ]
    });

    if (!invitation) {
      return res.status(404).json({ error: 'الدعوة غير موجودة' });
    }

    // Generate QR code as PNG buffer
    const qrCodeBuffer = await QRCode.toBuffer(invitation.qrCode, {
      type: 'png',
      width: 300,
      margin: 2
    });

    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Content-Disposition', `attachment; filename="invitation-${invitation.Guest.name.replace(/\s+/g, '-')}.png"`);
    res.send(qrCodeBuffer);
  } catch (error) {
    console.error('Download invitation QR error:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحميل رمز QR' });
  }
});

// Delete Guest
router.delete('/guests/:id', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const guestId = req.params.id;

    const guest = await Guest.findOne({
      where: { id: guestId },
      include: [
        { model: Party, as: 'Party', where: { ownerId: userId } }
      ]
    });

    if (!guest) {
      return res.status(404).json({ error: 'المدعو غير موجود' });
    }

    // Delete invitation if exists
    await Invitation.destroy({ where: { guestId } });
    
    // Delete guest
    await guest.destroy();

    res.json({ success: true, message: 'تم حذف المدعو بنجاح' });
  } catch (error) {
    console.error('Delete guest error:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حذف المدعو' });
  }
});

module.exports = router;
