const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [6, 255]
    }
  },
  fullName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'full_name',
    validate: {
      len: [2, 100]
    }
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [10, 20]
    }
  },
  role: {
    type: DataTypes.ENUM('admin', 'general_manager', 'venue_manager', 'party_owner', 'scanner'),
    allowNull: false,
    defaultValue: 'party_owner'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  // Foreign Keys
  managedVenueId: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'managed_venue_id',
    comment: 'القاعة التي يديرها هذا المستخدم (للمديرين فقط)'
  }
}, {
  tableName: 'users',
  freezeTableName: true,
  timestamps: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        user.password = await bcrypt.hash(user.password, 12);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        user.password = await bcrypt.hash(user.password, 12);
      }
    }
  }
});

// Associations
User.associate = (models) => {
  // مستخدم يمكن أن يدير قاعة واحدة
  User.belongsTo(models.Venue, {
    foreignKey: 'managedVenueId',
    as: 'managedVenue'
  });

  // مستخدم يمكن أن يملك عدة حفلات
  User.hasMany(models.Party, {
    foreignKey: 'ownerId',
    as: 'ownedParties'
  });

  // مستخدم يمكن أن يمسح عدة دعوات
  User.hasMany(models.Invitation, {
    foreignKey: 'scannedBy',
    as: 'scannedInvitations'
  });
};

// Instance methods
User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

User.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  delete values.password;
  return values;
};

User.prototype.canManageVenue = function(venueId) {
  return this.role === 'admin' ||
         this.role === 'general_manager' ||
         (this.role === 'venue_manager' && this.managedVenueId === venueId);
};

User.prototype.canCreateParty = function() {
  return ['admin', 'general_manager', 'party_owner'].includes(this.role);
};

// Class methods
User.findByEmail = function(email) {
  return this.findOne({ where: { email, isActive: true } });
};

User.findByRole = function(role) {
  return this.findAll({
    where: { role, isActive: true },
    order: [['fullName', 'ASC']]
  });
};

User.findManagers = function() {
  return this.findAll({
    where: {
      role: ['admin', 'general_manager', 'venue_manager'],
      isActive: true
    },
    order: [['fullName', 'ASC']]
  });
};

module.exports = User;
