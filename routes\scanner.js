const express = require('express');
const { User, Venue, Party, Invitation } = require('../models');
const { Op } = require('sequelize');

const router = express.Router();

// Scanner Dashboard
router.get('/dashboard', async (req, res) => {
  try {
    const userId = req.session.user.id;

    // Get assigned venue
    const assignedVenue = await Venue.findOne({
      include: [
        { model: User, as: 'manager', where: { managedVenueId: { [Op.ne]: null } } }
      ]
    });

    if (!assignedVenue) {
      return res.render('error', {
        title: 'خطأ',
        message: 'لم يتم تعيينك لأي قاعة بعد. يرجى التواصل مع مدير القاعة.'
      });
    }

    // Get today's parties
    const today = new Date().toISOString().split('T')[0];
    const todayParties = await Party.findAll({
      where: {
        venueId: assignedVenue.id,
        partyDate: today,
        status: ['confirmed', 'active']
      },
      include: [
        { model: User, as: 'owner', attributes: ['fullName'] }
      ],
      order: [['startTime', 'ASC']]
    });

    // Get scanning statistics
    const [totalScanned, todayScanned, successfulScans] = await Promise.all([
      Invitation.count({ where: { scannedBy: userId } }),
      Invitation.count({
        where: {
          scannedBy: userId,
          attendedAt: {
            [Op.gte]: new Date(today + ' 00:00:00'),
            [Op.lt]: new Date(today + ' 23:59:59')
          }
        }
      }),
      Invitation.count({
        where: {
          scannedBy: userId,
          status: 'attended'
        }
      })
    ]);

    res.render('scanner/dashboard', {
      title: 'لوحة تحكم الماسح',
      stats: {
        assignedHalls: assignedHalls.length,
        todayParties: todayParties.length,
        totalScanned,
        todayScanned,
        successfulScans
      },
      assignedHalls,
      todayParties
    });
  } catch (error) {
    console.error('Scanner dashboard error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل لوحة التحكم'
    });
  }
});

// Scan QR Code Page
router.get('/scan/:partyId?', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const partyId = req.params.partyId;

    // Get assigned halls
    const assignedHalls = await HallStaff.findAll({
      where: { userId, role: 'scanner' },
      include: [{ model: Hall, as: 'Hall' }]
    });

    const hallIds = assignedHalls.map(assignment => assignment.hallId);

    // Get today's parties
    const today = new Date().toISOString().split('T')[0];
    const availableParties = await Party.findAll({
      where: {
        hallId: { [Op.in]: hallIds },
        partyDate: today,
        status: ['planned', 'active']
      },
      include: [
        { model: Hall, as: 'Hall' },
        { model: User, as: 'Owner', attributes: ['fullName'] }
      ],
      order: [['startTime', 'ASC']]
    });

    let selectedParty = null;
    if (partyId) {
      selectedParty = availableParties.find(party => party.id === partyId);
    } else if (availableParties.length > 0) {
      selectedParty = availableParties[0];
    }

    res.render('scanner/scan', {
      title: 'مسح QR Code',
      availableParties,
      selectedParty
    });
  } catch (error) {
    console.error('Scan page error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل صفحة المسح'
    });
  }
});

// Process QR Code Scan
router.post('/scan', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const { qrCode, partyId } = req.body;

    if (!qrCode || !partyId) {
      return res.status(400).json({
        success: false,
        message: 'رمز QR ومعرف الحفلة مطلوبان'
      });
    }

    // Verify scanner has access to this party
    const party = await Party.findOne({
      where: { id: partyId },
      include: [
        {
          model: Hall,
          as: 'Hall',
          include: [
            {
              model: HallStaff,
              as: 'Staff',
              where: { userId, role: 'scanner' }
            }
          ]
        }
      ]
    });

    if (!party) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول لهذه الحفلة'
      });
    }

    // Find invitation by QR code
    const invitation = await Invitation.findOne({
      where: { qrCode },
      include: [
        {
          model: Guest,
          as: 'Guest',
          include: [
            { model: Party, as: 'Party' }
          ]
        }
      ]
    });

    if (!invitation) {
      return res.json({
        success: false,
        message: 'رمز QR غير صالح',
        status: 'invalid'
      });
    }

    // Check if invitation is for the correct party
    if (invitation.Guest.Party.id !== partyId) {
      return res.json({
        success: false,
        message: 'هذه الدعوة لحفلة أخرى',
        status: 'wrong_party',
        guestName: invitation.Guest.name,
        correctParty: invitation.Guest.Party.title
      });
    }

    // Check if already attended
    if (invitation.status === 'attended') {
      return res.json({
        success: false,
        message: 'تم تسجيل الحضور مسبقاً',
        status: 'already_attended',
        guestName: invitation.Guest.name,
        attendedAt: invitation.attendedAt
      });
    }

    // Mark as attended
    invitation.status = 'attended';
    invitation.attendedAt = new Date();
    invitation.scannedBy = userId;
    await invitation.save();

    res.json({
      success: true,
      message: 'تم تسجيل الحضور بنجاح',
      status: 'success',
      guestName: invitation.Guest.name,
      partyTitle: invitation.Guest.Party.title,
      attendedAt: invitation.attendedAt
    });

  } catch (error) {
    console.error('QR scan error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء معالجة رمز QR'
    });
  }
});

// Scan History
router.get('/history', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const { count, rows: scannedInvitations } = await Invitation.findAndCountAll({
      where: { scannedBy: userId },
      include: [
        {
          model: Guest,
          as: 'Guest',
          include: [
            {
              model: Party,
              as: 'Party',
              include: [{ model: Hall, as: 'Hall' }]
            }
          ]
        }
      ],
      order: [['attendedAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('scanner/history', {
      title: 'تاريخ المسح',
      scannedInvitations,
      pagination: {
        currentPage: page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Scan history error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل تاريخ المسح'
    });
  }
});

// Get Party Guests for Scanning
router.get('/parties/:id/guests', async (req, res) => {
  try {
    const userId = req.session.user.id;
    const partyId = req.params.id;

    // Verify scanner has access to this party
    const party = await Party.findOne({
      where: { id: partyId },
      include: [
        {
          model: Hall,
          as: 'Hall',
          include: [
            {
              model: HallStaff,
              as: 'Staff',
              where: { userId, role: 'scanner' }
            }
          ]
        },
        {
          model: Guest,
          as: 'Guests',
          include: [{ model: Invitation, as: 'Invitation' }]
        }
      ]
    });

    if (!party) {
      return res.status(403).json({ error: 'ليس لديك صلاحية للوصول لهذه الحفلة' });
    }

    // Calculate statistics
    const totalGuests = party.Guests.length;
    const attendedGuests = party.Guests.filter(guest => 
      guest.Invitation && guest.Invitation.status === 'attended'
    ).length;
    const pendingGuests = party.Guests.filter(guest => 
      guest.Invitation && guest.Invitation.status === 'pending'
    ).length;

    res.json({
      party: {
        id: party.id,
        title: party.title,
        hall: party.Hall.name
      },
      stats: {
        totalGuests,
        attendedGuests,
        pendingGuests
      },
      guests: party.Guests.map(guest => ({
        id: guest.id,
        name: guest.name,
        phone: guest.phone,
        invitation: guest.Invitation ? {
          id: guest.Invitation.id,
          status: guest.Invitation.status,
          attendedAt: guest.Invitation.attendedAt
        } : null
      }))
    });
  } catch (error) {
    console.error('Get party guests error:', error);
    res.status(500).json({ error: 'حدث خطأ في تحميل قائمة المدعوين' });
  }
});

module.exports = router;
