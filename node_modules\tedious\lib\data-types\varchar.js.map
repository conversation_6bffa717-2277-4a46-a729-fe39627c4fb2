{"version": 3, "file": "varchar.js", "names": ["_iconvLite", "_interopRequireDefault", "require", "obj", "__esModule", "default", "MAX", "UNKNOWN_PLP_LEN", "<PERSON><PERSON><PERSON>", "from", "PLP_TERMINATOR", "NULL_LENGTH", "MAX_NULL_LENGTH", "VarChar", "id", "type", "name", "maximumLength", "declaration", "parameter", "value", "length", "output", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeUInt16LE", "collation", "<PERSON><PERSON><PERSON><PERSON>", "copy", "generateParameterLength", "options", "generateParameterData", "writeUInt32LE", "validate", "TypeError", "Error", "codepage", "iconv", "encode", "_default", "exports", "module"], "sources": ["../../src/data-types/varchar.ts"], "sourcesContent": ["import iconv from 'iconv-lite';\n\nimport { type DataType } from '../data-type';\n\nconst MAX = (1 << 16) - 1;\nconst UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);\nconst PLP_TERMINATOR = Buffer.from([0x00, 0x00, 0x00, 0x00]);\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\nconst MAX_NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF]);\n\nconst VarChar: { maximumLength: number } & DataType = {\n  id: 0xA7,\n  type: 'BIGVARCHR',\n  name: 'VarChar',\n  maximumLength: 8000,\n\n  declaration: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    let length;\n    if (parameter.length) {\n      length = parameter.length;\n    } else if (value != null) {\n      length = value.length || 1;\n    } else if (value === null && !parameter.output) {\n      length = 1;\n    } else {\n      length = this.maximumLength;\n    }\n\n    if (length <= this.maximumLength) {\n      return 'varchar(' + length + ')';\n    } else {\n      return 'varchar(max)';\n    }\n  },\n\n  resolveLength: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    if (parameter.length != null) {\n      return parameter.length;\n    } else if (value != null) {\n      return value.length || 1;\n    } else {\n      return this.maximumLength;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    const buffer = Buffer.alloc(8);\n    buffer.writeUInt8(this.id, 0);\n\n    if (parameter.length! <= this.maximumLength) {\n      buffer.writeUInt16LE(parameter.length!, 1);\n    } else {\n      buffer.writeUInt16LE(MAX, 1);\n    }\n\n    if (parameter.collation) {\n      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    const value = parameter.value as Buffer | null;\n\n    if (value == null) {\n      if (parameter.length! <= this.maximumLength) {\n        return NULL_LENGTH;\n      } else {\n        return MAX_NULL_LENGTH;\n      }\n    }\n\n    if (parameter.length! <= this.maximumLength) {\n      const buffer = Buffer.alloc(2);\n      buffer.writeUInt16LE(value.length, 0);\n      return buffer;\n    } else {\n      return UNKNOWN_PLP_LEN;\n    }\n  },\n\n  *generateParameterData(parameter, options) {\n    const value = parameter.value as Buffer | null;\n\n    if (value == null) {\n      return;\n    }\n\n    if (parameter.length! <= this.maximumLength) {\n      yield value;\n    } else {\n      if (value.length > 0) {\n        const buffer = Buffer.alloc(4);\n        buffer.writeUInt32LE(value.length, 0);\n        yield buffer;\n\n        yield value;\n      }\n\n      yield PLP_TERMINATOR;\n    }\n  },\n\n  validate: function(value, collation): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    if (!collation) {\n      throw new Error('No collation was set by the server for the current connection.');\n    }\n\n    if (!collation.codepage) {\n      throw new Error('The collation set by the server has no associated encoding.');\n    }\n\n    return iconv.encode(value, collation.codepage);\n  }\n};\n\nexport default VarChar;\nmodule.exports = VarChar;\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA+B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAI/B,MAAMG,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;AACzB,MAAMC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACrF,MAAMC,cAAc,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAE5D,MAAME,WAAW,GAAGH,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7C,MAAMG,eAAe,GAAGJ,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAErF,MAAMI,OAA6C,GAAG;EACpDC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,SAAS;EACfC,aAAa,EAAE,IAAI;EAEnBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIC,MAAM;IACV,IAAIF,SAAS,CAACE,MAAM,EAAE;MACpBA,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3B,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxBC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAID,KAAK,KAAK,IAAI,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;MAC9CD,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI,CAACJ,aAAa;IAC7B;IAEA,IAAII,MAAM,IAAI,IAAI,CAACJ,aAAa,EAAE;MAChC,OAAO,UAAU,GAAGI,MAAM,GAAG,GAAG;IAClC,CAAC,MAAM;MACL,OAAO,cAAc;IACvB;EACF,CAAC;EAEDE,aAAa,EAAE,SAAAA,CAASJ,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAID,SAAS,CAACE,MAAM,IAAI,IAAI,EAAE;MAC5B,OAAOF,SAAS,CAACE,MAAM;IACzB,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAOA,KAAK,CAACC,MAAM,IAAI,CAAC;IAC1B,CAAC,MAAM;MACL,OAAO,IAAI,CAACJ,aAAa;IAC3B;EACF,CAAC;EAEDO,gBAAgBA,CAACL,SAAS,EAAE;IAC1B,MAAMM,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACb,EAAE,EAAE,CAAC,CAAC;IAE7B,IAAIK,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3CQ,MAAM,CAACG,aAAa,CAACT,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLI,MAAM,CAACG,aAAa,CAACtB,GAAG,EAAE,CAAC,CAAC;IAC9B;IAEA,IAAIa,SAAS,CAACU,SAAS,EAAE;MACvBV,SAAS,CAACU,SAAS,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDO,uBAAuBA,CAACb,SAAS,EAAEc,OAAO,EAAE;IAC1C,MAAMb,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;QAC3C,OAAON,WAAW;MACpB,CAAC,MAAM;QACL,OAAOC,eAAe;MACxB;IACF;IAEA,IAAIO,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3C,MAAMQ,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;MAC9BD,MAAM,CAACG,aAAa,CAACR,KAAK,CAACC,MAAM,EAAE,CAAC,CAAC;MACrC,OAAOI,MAAM;IACf,CAAC,MAAM;MACL,OAAOlB,eAAe;IACxB;EACF,CAAC;EAED,CAAC2B,qBAAqBA,CAACf,SAAS,EAAEc,OAAO,EAAE;IACzC,MAAMb,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB;IACF;IAEA,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3C,MAAMG,KAAK;IACb,CAAC,MAAM;MACL,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACpB,MAAMI,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;QAC9BD,MAAM,CAACU,aAAa,CAACf,KAAK,CAACC,MAAM,EAAE,CAAC,CAAC;QACrC,MAAMI,MAAM;QAEZ,MAAML,KAAK;MACb;MAEA,MAAMV,cAAc;IACtB;EACF,CAAC;EAED0B,QAAQ,EAAE,SAAAA,CAAShB,KAAK,EAAES,SAAS,EAAiB;IAClD,IAAIT,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIiB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAI,CAACR,SAAS,EAAE;MACd,MAAM,IAAIS,KAAK,CAAC,gEAAgE,CAAC;IACnF;IAEA,IAAI,CAACT,SAAS,CAACU,QAAQ,EAAE;MACvB,MAAM,IAAID,KAAK,CAAC,6DAA6D,CAAC;IAChF;IAEA,OAAOE,kBAAK,CAACC,MAAM,CAACrB,KAAK,EAAES,SAAS,CAACU,QAAQ,CAAC;EAChD;AACF,CAAC;AAAC,IAAAG,QAAA,GAEa7B,OAAO;AAAA8B,OAAA,CAAAtC,OAAA,GAAAqC,QAAA;AACtBE,MAAM,CAACD,OAAO,GAAG9B,OAAO"}