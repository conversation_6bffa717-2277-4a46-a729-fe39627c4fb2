<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إدارة الدعوات - {{party.title}}</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/party-owner/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="/party-owner/parties">حفلاتي</a></li>
            <li class="breadcrumb-item active">إدارة الدعوات</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  {{#if success}}
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    {{success}}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  {{/if}}

  <!-- Party Info -->
  <div class="row">
    <div class="col-12">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h5 class="text-white mb-1">{{party.title}}</h5>
              <p class="text-white-50 mb-0">{{party.venue.name}} - {{formatDate party.partyDate}} - {{party.startTime}}</p>
              <p class="text-white-50 mb-0">عدد الضيوف المتوقع: {{party.expectedGuests}}</p>
            </div>
            <div class="col-md-4 text-end">
              <h3 class="text-white mb-0">{{stats.totalInvitations}}</h3>
              <p class="text-white-50 mb-0">إجمالي الدعوات</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row">
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <h3 class="text-primary">{{stats.sentInvitations}}</h3>
          <p class="text-muted mb-0">دعوات مُرسلة</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <h3 class="text-success">{{stats.attendedGuests}}</h3>
          <p class="text-muted mb-0">ضيوف حضروا</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <h3 class="text-warning">{{subtract stats.totalInvitations stats.sentInvitations}}</h3>
          <p class="text-muted mb-0">دعوات معلقة</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <h3 class="text-info">{{percentage stats.attendedGuests stats.totalInvitations}}%</h3>
          <p class="text-muted mb-0">معدل الحضور</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <h4 class="header-title">قائمة الدعوات</h4>
            </div>
            <div class="col-md-6 text-end">
              <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" onclick="showAddGuestModal()">
                  <i class="mdi mdi-plus me-1"></i>
                  إضافة ضيف
                </button>
                <button type="button" class="btn btn-success" onclick="showBulkAddModal()">
                  <i class="mdi mdi-file-excel me-1"></i>
                  إضافة متعددة
                </button>
                <button type="button" class="btn btn-info" onclick="generateAllQRCodes()">
                  <i class="mdi mdi-qrcode me-1"></i>
                  إنشاء جميع الرموز
                </button>
                <button type="button" class="btn btn-warning" onclick="sendAllInvitations()">
                  <i class="mdi mdi-send me-1"></i>
                  إرسال الدعوات
                </button>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="row mb-3">
            <div class="col-md-4">
              <select class="form-select" id="statusFilter" onchange="filterInvitations()">
                <option value="">جميع الحالات</option>
                <option value="pending">معلقة</option>
                <option value="sent">مُرسلة</option>
                <option value="confirmed">مؤكدة</option>
                <option value="attended">حضر</option>
                <option value="no_show">لم يحضر</option>
                <option value="cancelled">ملغية</option>
              </select>
            </div>
            <div class="col-md-8">
              <input type="text" class="form-control" id="searchInput" placeholder="البحث في الضيوف..." onkeyup="searchInvitations()">
            </div>
          </div>

          <!-- Invitations Table -->
          <div class="table-responsive">
            <table class="table table-centered table-striped" id="invitationsTable">
              <thead>
                <tr>
                  <th>
                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                  </th>
                  <th>اسم الضيف</th>
                  <th>رقم الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>رمز QR</th>
                  <th>الحالة</th>
                  <th>وقت الحضور</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {{#each party.invitations}}
                <tr data-status="{{status}}">
                  <td>
                    <input type="checkbox" class="invitation-checkbox" value="{{id}}">
                  </td>
                  <td>
                    <strong>{{guestName}}</strong>
                    {{#if notes}}
                    <br><small class="text-muted">{{notes}}</small>
                    {{/if}}
                  </td>
                  <td>{{#if guestPhone}}{{guestPhone}}{{else}}-{{/if}}</td>
                  <td>{{#if guestEmail}}{{guestEmail}}{{else}}-{{/if}}</td>
                  <td>
                    {{#if qrCode}}
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="showQRCode('{{id}}', '{{qrCode}}')">
                      <i class="mdi mdi-qrcode"></i>
                    </button>
                    {{else}}
                    <span class="text-muted">غير متوفر</span>
                    {{/if}}
                  </td>
                  <td>
                    {{#eq status 'pending'}}
                    <span class="badge bg-secondary">معلقة</span>
                    {{/eq}}
                    {{#eq status 'sent'}}
                    <span class="badge bg-info">مُرسلة</span>
                    {{/eq}}
                    {{#eq status 'confirmed'}}
                    <span class="badge bg-primary">مؤكدة</span>
                    {{/eq}}
                    {{#eq status 'attended'}}
                    <span class="badge bg-success">حضر</span>
                    {{/eq}}
                    {{#eq status 'no_show'}}
                    <span class="badge bg-warning">لم يحضر</span>
                    {{/eq}}
                    {{#eq status 'cancelled'}}
                    <span class="badge bg-danger">ملغية</span>
                    {{/eq}}
                  </td>
                  <td>
                    {{#if attendedAt}}
                    {{formatDateTime attendedAt}}
                    {{else}}
                    -
                    {{/if}}
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="editGuest('{{id}}')" title="تعديل">
                        <i class="mdi mdi-pencil"></i>
                      </button>
                      {{#if qrCode}}
                      <button type="button" class="btn btn-sm btn-outline-info" onclick="downloadQRCode('{{id}}')" title="تحميل QR">
                        <i class="mdi mdi-download"></i>
                      </button>
                      {{/if}}
                      {{#eq status 'pending'}}
                      <button type="button" class="btn btn-sm btn-outline-success" onclick="sendInvitation('{{id}}')" title="إرسال الدعوة">
                        <i class="mdi mdi-send"></i>
                      </button>
                      {{/eq}}
                      <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteInvitation('{{id}}')" title="حذف">
                        <i class="mdi mdi-delete"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {{else}}
                <tr>
                  <td colspan="8" class="text-center">لا توجد دعوات</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>

          <!-- Bulk Actions -->
          <div class="row mt-3">
            <div class="col-12">
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-primary" onclick="bulkAction('send')" disabled id="bulkSendBtn">
                  <i class="mdi mdi-send me-1"></i>
                  إرسال المحدد
                </button>
                <button type="button" class="btn btn-outline-info" onclick="bulkAction('qr')" disabled id="bulkQRBtn">
                  <i class="mdi mdi-qrcode me-1"></i>
                  تحميل QR المحدد
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="bulkAction('delete')" disabled id="bulkDeleteBtn">
                  <i class="mdi mdi-delete me-1"></i>
                  حذف المحدد
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Guest Modal -->
<div class="modal fade" id="addGuestModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">إضافة ضيف جديد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="addGuestForm" action="/party-owner/parties/{{party.id}}/guests/add" method="POST">
          <div class="mb-3">
            <label for="guestName" class="form-label">اسم الضيف <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="guestName" name="guestName" required>
          </div>
          <div class="mb-3">
            <label for="guestPhone" class="form-label">رقم الهاتف</label>
            <input type="tel" class="form-control" id="guestPhone" name="guestPhone" placeholder="05xxxxxxxx">
          </div>
          <div class="mb-3">
            <label for="guestEmail" class="form-label">البريد الإلكتروني</label>
            <input type="email" class="form-control" id="guestEmail" name="guestEmail">
          </div>
          <div class="mb-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="submitAddGuest()">إضافة الضيف</button>
      </div>
    </div>
  </div>
</div>

<!-- QR Code Modal -->
<div class="modal fade" id="qrCodeModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">رمز QR للدعوة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body text-center" id="qrCodeBody">
        <!-- QR Code will be displayed here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
        <button type="button" class="btn btn-primary" onclick="downloadCurrentQR()">تحميل</button>
      </div>
    </div>
  </div>
</div>

<script>
let selectedInvitations = [];
let currentQRCode = null;

function showAddGuestModal() {
  new bootstrap.Modal(document.getElementById('addGuestModal')).show();
}

function submitAddGuest() {
  document.getElementById('addGuestForm').submit();
}

function showQRCode(invitationId, qrCode) {
  currentQRCode = qrCode;
  // Generate QR code image
  const qrCodeBody = document.getElementById('qrCodeBody');
  qrCodeBody.innerHTML = `
    <div id="qrcode-${invitationId}"></div>
    <p class="mt-2"><strong>رمز الدعوة:</strong> ${qrCode}</p>
  `;
  
  // You would use a QR code library here like qrcode.js
  // For now, showing placeholder
  document.getElementById(`qrcode-${invitationId}`).innerHTML = `
    <div class="bg-light p-4 rounded">
      <i class="mdi mdi-qrcode font-48"></i>
      <p class="mt-2">رمز QR: ${qrCode}</p>
    </div>
  `;
  
  new bootstrap.Modal(document.getElementById('qrCodeModal')).show();
}

function toggleSelectAll() {
  const selectAll = document.getElementById('selectAll');
  const checkboxes = document.querySelectorAll('.invitation-checkbox');
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = selectAll.checked;
  });
  
  updateSelectedInvitations();
}

function updateSelectedInvitations() {
  const checkboxes = document.querySelectorAll('.invitation-checkbox:checked');
  selectedInvitations = Array.from(checkboxes).map(cb => cb.value);
  
  // Update bulk action buttons
  const hasSelection = selectedInvitations.length > 0;
  document.getElementById('bulkSendBtn').disabled = !hasSelection;
  document.getElementById('bulkQRBtn').disabled = !hasSelection;
  document.getElementById('bulkDeleteBtn').disabled = !hasSelection;
}

function filterInvitations() {
  const filter = document.getElementById('statusFilter').value;
  const rows = document.querySelectorAll('#invitationsTable tbody tr');
  
  rows.forEach(row => {
    if (!filter || row.dataset.status === filter) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

function searchInvitations() {
  const input = document.getElementById('searchInput').value.toLowerCase();
  const rows = document.querySelectorAll('#invitationsTable tbody tr');
  
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    if (text.includes(input)) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

function sendInvitation(invitationId) {
  if (confirm('هل أنت متأكد من إرسال هذه الدعوة؟')) {
    fetch(`/party-owner/invitations/${invitationId}/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert(data.error || 'حدث خطأ في إرسال الدعوة');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في إرسال الدعوة');
    });
  }
}

function deleteInvitation(invitationId) {
  if (confirm('هل أنت متأكد من حذف هذه الدعوة؟')) {
    fetch(`/party-owner/invitations/${invitationId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert(data.error || 'حدث خطأ في حذف الدعوة');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في حذف الدعوة');
    });
  }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
  // Add change listeners to checkboxes
  document.querySelectorAll('.invitation-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedInvitations);
  });
});
</script>
