// models/index.js
const User = require('./User');
const Venue = require('./Venue');
const Party = require('./Party');
const Invitation = require('./Invitation');
const Admin = require('./admin');

// تعريف العلاقات
User.hasMany(Venue, { foreignKey: 'managerId', as: 'ManagedVenues' });
User.hasMany(Venue, { foreignKey: 'createdBy', as: 'CreatedVenues' });
User.hasMany(Party, { foreignKey: 'ownerId', as: 'OwnedParties' });
User.hasMany(Invitation, { foreignKey: 'scannedBy', as: 'ScannedInvitations' });
User.belongsTo(Venue, { foreignKey: 'venueId', as: 'AssignedVenue' });

Venue.belongsTo(User, { foreignKey: 'managerId', as: 'Manager' });
Venue.belongsTo(User, { foreignKey: 'createdBy', as: 'Creator' });
Venue.hasMany(Party, { foreignKey: 'venueId', as: 'Parties' });
Venue.hasMany(User, { foreignKey: 'venueId', as: 'AssignedUsers' });

Party.belongsTo(Venue, { foreignKey: 'venueId', as: 'Venue' });
Party.belongsTo(User, { foreignKey: 'ownerId', as: 'Owner' });
Party.hasMany(Invitation, { foreignKey: 'partyId', as: 'Invitations' });

Invitation.belongsTo(Party, { foreignKey: 'partyId', as: 'Party' });
Invitation.belongsTo(User, { foreignKey: 'scannedBy', as: 'Scanner' });

module.exports = {
  User,
  Venue,
  Party,
  Invitation,
  Admin
};
