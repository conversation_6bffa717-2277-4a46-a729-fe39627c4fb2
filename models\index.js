const sequelize = require('../config/database');

// Import models
const User = require('./User');
const Venue = require('./Venue');
const Party = require('./Party');
const Invitation = require('./Invitation');

// Create models object
const models = {
  User,
  Venue,
  Party,
  Invitation,
  sequelize
};

// Initialize associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

module.exports = models;
