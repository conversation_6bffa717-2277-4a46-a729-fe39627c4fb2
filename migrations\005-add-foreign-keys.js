'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add foreign key constraint for venues.created_by -> users.id
    await queryInterface.addConstraint('venues', {
      fields: ['created_by'],
      type: 'foreign key',
      name: 'fk_venues_created_by',
      references: {
        table: 'users',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'NO ACTION'
    });

    // Skip circular reference between users and venues for now
    // Will be handled through application logic

    // Add foreign key constraint for parties.venue_id -> venues.id
    await queryInterface.addConstraint('parties', {
      fields: ['venue_id'],
      type: 'foreign key',
      name: 'fk_parties_venue_id',
      references: {
        table: 'venues',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'NO ACTION'
    });

    // Add foreign key constraint for parties.owner_id -> users.id
    await queryInterface.addConstraint('parties', {
      fields: ['owner_id'],
      type: 'foreign key',
      name: 'fk_parties_owner_id',
      references: {
        table: 'users',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'NO ACTION'
    });

    // Add foreign key constraint for invitations.party_id -> parties.id
    await queryInterface.addConstraint('invitations', {
      fields: ['party_id'],
      type: 'foreign key',
      name: 'fk_invitations_party_id',
      references: {
        table: 'parties',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });

    // Add foreign key constraint for invitations.scanned_by -> users.id
    await queryInterface.addConstraint('invitations', {
      fields: ['scanned_by'],
      type: 'foreign key',
      name: 'fk_invitations_scanned_by',
      references: {
        table: 'users',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove foreign key constraints in reverse order
    await queryInterface.removeConstraint('invitations', 'fk_invitations_scanned_by');
    await queryInterface.removeConstraint('invitations', 'fk_invitations_party_id');
    await queryInterface.removeConstraint('parties', 'fk_parties_owner_id');
    await queryInterface.removeConstraint('parties', 'fk_parties_venue_id');
    await queryInterface.removeConstraint('venues', 'fk_venues_created_by');
  }
};
