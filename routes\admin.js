const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, Party, Venue, Invitation } = require('../models');
const { Op } = require('sequelize');

const router = express.Router();

// Admin Dashboard
router.get('/dashboard', async (req, res) => {
  try {
    // Get statistics
    const [totalHalls, totalManagers, totalParties, activeParties] = await Promise.all([
      Venue.count({ where: { isActive: true } }),
      User.count({ where: { role: 'hall_manager', isActive: true } }),
      Party.count(),
      Party.count({ where: { status: 'active' } })
    ]);

    // Get recent halls
    const recentHalls = await Venue.findAll({
      include: [
        { model: User, as: 'Manager', attributes: ['fullName'] },
        { model: User, as: 'Creator', attributes: ['fullName'] }
      ],
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    res.render('admin/dashboard', {
      title: 'لوحة تحكم المدير',
      stats: {
        totalHalls,
        totalManagers,
        totalParties,
        activeParties
      },
      recentHalls
    });
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل لوحة التحكم'
    });
  }
});

// Halls Management
router.get('/halls', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;

    const { count, rows: Venues } = await Venue.findAndCountAll({
      include: [
        { model: User, as: 'Manager', attributes: ['fullName'] },
        { model: User, as: 'Creator', attributes: ['fullName'] }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/halls', {
      title: 'إدارة الصالات',
      Venues,
      pagination: {
        currentPage: page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Halls list error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل قائمة الصالات'
    });
  }
});

// Create Hall Page
router.get('/halls/create', async (req, res) => {
  try {
    const hallManagers = await User.findByRole('hall_manager');
    res.render('admin/create-hall', {
      title: 'إنشاء صالة جديدة',
      hallManagers
    });
  } catch (error) {
    console.error('Create hall page error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل صفحة إنشاء الصالة'
    });
  }
});

// Create Hall Process
router.post('/halls/create', [
  body('name').isLength({ min: 2 }).withMessage('اسم الصالة مطلوب'),
  body('location').notEmpty().withMessage('موقع الصالة مطلوب'),
  body('capacity').isInt({ min: 1 }).withMessage('سعة الصالة يجب أن تكون رقم صحيح أكبر من 0'),
  body('managerId').optional().isUUID().withMessage('مدير الصالة غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const hallManagers = await User.findByRole('hall_manager');
      return res.render('admin/create-hall', {
        title: 'إنشاء صالة جديدة',
        hallManagers,
        errors: errors.array(),
        formData: req.body
      });
    }

    const { name, location, capacity, description, managerId } = req.body;

    await Hall.create({
      name,
      location,
      capacity: parseInt(capacity),
      description: description || null,
      managerId: managerId || null,
      createdBy: req.session.user.id
    });

    res.redirect('/admin/halls?success=تم إنشاء الصالة بنجاح');
  } catch (error) {
    console.error('Create hall error:', error);
    const hallManagers = await User.findByRole('hall_manager');
    res.render('admin/create-hall', {
      title: 'إنشاء صالة جديدة',
      hallManagers,
      error: 'حدث خطأ أثناء إنشاء الصالة',
      formData: req.body
    });
  }
});

// Hall Managers Management
router.get('/managers', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;

    const { count, rows: managers } = await User.findAndCountAll({
      where: { role: 'hall_manager' },
      include: [
        { model: Hall, as: 'ManagedHalls', attributes: ['name'] }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/managers', {
      title: 'إدارة مديري الصالات',
      managers,
      pagination: {
        currentPage: page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Managers list error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل قائمة مديري الصالات'
    });
  }
});

// Create Manager Page
router.get('/managers/create', (req, res) => {
  res.render('admin/create-manager', {
    title: 'إنشاء مدير صالة جديد'
  });
});

// Create Manager Process
router.post('/managers/create', [
  body('fullName').isLength({ min: 2 }).withMessage('الاسم الكامل مطلوب'),
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('phone').optional().isMobilePhone('ar-SA').withMessage('رقم الهاتف غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('admin/create-manager', {
        title: 'إنشاء مدير صالة جديد',
        errors: errors.array(),
        formData: req.body
      });
    }

    const { fullName, email, password, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.render('admin/create-manager', {
        title: 'إنشاء مدير صالة جديد',
        error: 'البريد الإلكتروني مستخدم بالفعل',
        formData: req.body
      });
    }

    await User.create({
      fullName,
      email,
      password,
      phone: phone || null,
      role: 'hall_manager'
    });

    res.redirect('/admin/managers?success=تم إنشاء مدير الصالة بنجاح');
  } catch (error) {
    console.error('Create manager error:', error);
    res.render('admin/create-manager', {
      title: 'إنشاء مدير صالة جديد',
      error: 'حدث خطأ أثناء إنشاء مدير الصالة',
      formData: req.body
    });
  }
});

// Delete Hall
router.delete('/halls/:id', async (req, res) => {
  try {
    const hall = await Hall.findByPk(req.params.id);
    if (!hall) {
      return res.status(404).json({ error: 'الصالة غير موجودة' });
    }

    // Check if hall has active parties
    const activeParties = await Party.count({
      where: {
        hallId: hall.id,
        status: ['planned', 'active']
      }
    });

    if (activeParties > 0) {
      return res.status(400).json({ 
        error: 'لا يمكن حذف الصالة لوجود حفلات نشطة بها' 
      });
    }

    await hall.destroy();
    res.json({ success: true, message: 'تم حذف الصالة بنجاح' });
  } catch (error) {
    console.error('Delete hall error:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حذف الصالة' });
  }
});

// Toggle Manager Status
router.patch('/managers/:id/toggle-status', async (req, res) => {
  try {
    const manager = await User.findByPk(req.params.id);
    if (!manager || manager.role !== 'hall_manager') {
      return res.status(404).json({ error: 'مدير الصالة غير موجود' });
    }

    manager.isActive = !manager.isActive;
    await manager.save();

    res.json({ 
      success: true, 
      message: `تم ${manager.isActive ? 'تفعيل' : 'إلغاء تفعيل'} مدير الصالة بنجاح`,
      isActive: manager.isActive
    });
  } catch (error) {
    console.error('Toggle manager status error:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحديث حالة مدير الصالة' });
  }
});

module.exports = router;
