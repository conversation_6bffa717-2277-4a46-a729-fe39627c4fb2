<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">لوحة تحكم الماسح الضوئي</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
            <li class="breadcrumb-item active">لوحة التحكم</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Venue Info -->
  {{#if assignedVenue}}
  <div class="row">
    <div class="col-12">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-8">
              <h4 class="text-white mb-1">{{assignedVenue.name}}</h4>
              <p class="text-white-50 mb-0">{{assignedVenue.location}}</p>
              <p class="text-white-50 mb-0">السعة: {{assignedVenue.capacity}} شخص</p>
            </div>
            <div class="col-4 text-end">
              <i class="mdi mdi-qrcode-scan widget-icon bg-white-10"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {{/if}}

  <!-- Statistics Cards -->
  <div class="row">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-6">
              <h5 class="text-muted fw-normal mt-0 text-truncate" title="مسح اليوم">مسح اليوم</h5>
              <h3 class="my-2 py-1">{{stats.totalScansToday}}</h3>
            </div>
            <div class="col-6">
              <div class="text-end">
                <div id="today-scans-chart" data-colors="#00acc1"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-6">
              <h5 class="text-muted fw-normal mt-0 text-truncate" title="مسح هذا الأسبوع">مسح الأسبوع</h5>
              <h3 class="my-2 py-1">{{stats.totalScansThisWeek}}</h3>
            </div>
            <div class="col-6">
              <div class="text-end">
                <div id="week-scans-chart" data-colors="#43d39e"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-6">
              <h5 class="text-muted fw-normal mt-0 text-truncate" title="الحفلات النشطة">الحفلات النشطة</h5>
              <h3 class="my-2 py-1">{{stats.activeParties}}</h3>
            </div>
            <div class="col-6">
              <div class="text-end">
                <div id="active-parties-chart" data-colors="#f77e53"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-6">
              <h5 class="text-muted fw-normal mt-0 text-truncate" title="حفلات اليوم">حفلات اليوم</h5>
              <h3 class="my-2 py-1">{{stats.todayParties}}</h3>
            </div>
            <div class="col-6">
              <div class="text-end">
                <div id="today-parties-chart" data-colors="#4fc6e1"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title mb-3">الإجراءات السريعة</h4>
          <div class="row">
            <div class="col-md-4">
              <a href="/scanner/scan" class="btn btn-primary btn-lg w-100 mb-2">
                <i class="mdi mdi-qrcode-scan me-1"></i>
                مسح رموز QR
              </a>
            </div>
            <div class="col-md-4">
              <a href="/scanner/activity" class="btn btn-info btn-lg w-100 mb-2">
                <i class="mdi mdi-chart-line me-1"></i>
                تقرير النشاط
              </a>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-success btn-lg w-100 mb-2" onclick="refreshData()">
                <i class="mdi mdi-refresh me-1"></i>
                تحديث البيانات
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Today's Parties -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title">حفلات اليوم</h4>
          {{#if todayParties}}
          <div class="table-responsive">
            <table class="table table-centered table-striped">
              <thead>
                <tr>
                  <th>عنوان الحفلة</th>
                  <th>صاحب الحفلة</th>
                  <th>الوقت</th>
                  <th>عدد الدعوات</th>
                  <th>الحضور</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {{#each todayParties}}
                <tr>
                  <td>
                    <strong>{{title}}</strong>
                    {{#if type}}
                    <br><small class="text-muted">{{getPartyTypeText type}}</small>
                    {{/if}}
                  </td>
                  <td>{{owner.fullName}}</td>
                  <td>{{startTime}} - {{endTime}}</td>
                  <td>
                    <span class="badge bg-info">{{invitations.length}}</span>
                  </td>
                  <td>
                    <span class="badge bg-success">{{getAttendedCount invitations}}</span>
                  </td>
                  <td>
                    {{#eq status 'confirmed'}}
                    <span class="badge bg-info">مؤكد</span>
                    {{/eq}}
                    {{#eq status 'active'}}
                    <span class="badge bg-success">نشط</span>
                    {{/eq}}
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewPartyGuests('{{id}}')" title="عرض الضيوف">
                        <i class="mdi mdi-account-group"></i>
                      </button>
                      <a href="/scanner/scan?party={{id}}" class="btn btn-sm btn-outline-success" title="مسح الدعوات">
                        <i class="mdi mdi-qrcode-scan"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>
          {{else}}
          <div class="text-center py-4">
            <div class="avatar-lg mx-auto mb-4">
              <div class="avatar-title bg-soft-info text-info rounded-circle">
                <i class="mdi mdi-calendar-today font-24"></i>
              </div>
            </div>
            <h5>لا توجد حفلات اليوم</h5>
            <p class="text-muted">لا توجد حفلات مجدولة لهذا اليوم في القاعة المُعيَّنة لك</p>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>

  <!-- Scanner Instructions -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title">إرشادات المسح الضوئي</h4>
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-primary">خطوات المسح:</h6>
              <ol class="list-unstyled">
                <li><i class="mdi mdi-numeric-1-circle text-primary me-2"></i>اختر الحفلة النشطة</li>
                <li><i class="mdi mdi-numeric-2-circle text-primary me-2"></i>امسح رمز QR للضيف</li>
                <li><i class="mdi mdi-numeric-3-circle text-primary me-2"></i>تأكد من صحة البيانات</li>
                <li><i class="mdi mdi-numeric-4-circle text-primary me-2"></i>اضغط تأكيد الحضور</li>
              </ol>
            </div>
            <div class="col-md-6">
              <h6 class="text-primary">نصائح مهمة:</h6>
              <ul class="list-unstyled">
                <li><i class="mdi mdi-check text-success me-2"></i>تأكد من وضوح رمز QR</li>
                <li><i class="mdi mdi-check text-success me-2"></i>تحقق من اسم الضيف</li>
                <li><i class="mdi mdi-check text-success me-2"></i>لا تمسح نفس الرمز مرتين</li>
                <li><i class="mdi mdi-check text-success me-2"></i>أبلغ عن أي مشاكل فوراً</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Party Guests Modal -->
<div class="modal fade" id="partyGuestsModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">قائمة ضيوف الحفلة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="partyGuestsBody">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

<script>
function viewPartyGuests(partyId) {
  fetch(`/scanner/parties/${partyId}/guests`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        let guestsHtml = `
          <div class="mb-3">
            <h6>${data.party.title}</h6>
            <p class="text-muted">صاحب الحفلة: ${data.party.owner} | التاريخ: ${new Date(data.party.partyDate).toLocaleDateString('ar-SA')}</p>
          </div>
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>اسم الضيف</th>
                  <th>رقم الهاتف</th>
                  <th>الحالة</th>
                  <th>وقت الحضور</th>
                </tr>
              </thead>
              <tbody>
        `;
        
        data.invitations.forEach(invitation => {
          const statusBadge = getStatusBadge(invitation.status);
          const attendedTime = invitation.attendedAt ? new Date(invitation.attendedAt).toLocaleString('ar-SA') : '-';
          
          guestsHtml += `
            <tr>
              <td>${invitation.guestName}</td>
              <td>${invitation.guestPhone || '-'}</td>
              <td>${statusBadge}</td>
              <td>${attendedTime}</td>
            </tr>
          `;
        });
        
        guestsHtml += `
              </tbody>
            </table>
          </div>
        `;
        
        document.getElementById('partyGuestsBody').innerHTML = guestsHtml;
        new bootstrap.Modal(document.getElementById('partyGuestsModal')).show();
      } else {
        alert(data.error || 'حدث خطأ في تحميل قائمة الضيوف');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل قائمة الضيوف');
    });
}

function getStatusBadge(status) {
  const badges = {
    'pending': '<span class="badge bg-secondary">معلقة</span>',
    'sent': '<span class="badge bg-info">مُرسلة</span>',
    'confirmed': '<span class="badge bg-primary">مؤكدة</span>',
    'attended': '<span class="badge bg-success">حضر</span>',
    'no_show': '<span class="badge bg-warning">لم يحضر</span>',
    'cancelled': '<span class="badge bg-danger">ملغية</span>'
  };
  return badges[status] || status;
}

function refreshData() {
  location.reload();
}

// Auto refresh every 5 minutes
setInterval(function() {
  location.reload();
}, 5 * 60 * 1000);
</script>
