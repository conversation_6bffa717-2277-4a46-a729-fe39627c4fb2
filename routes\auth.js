const express = require('express');
const { body, validationResult } = require('express-validator');
const { User } = require('../models');
const { redirectIfAuthenticated } = require('../middleware/auth');

const router = express.Router();

// Login page
router.get('/login', redirectIfAuthenticated, (req, res) => {
  res.render('auth/login', {
    title: 'تسجيل الدخول',
    layout: 'auth'
  });
});

// Login process
router.post('/login', [
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').notEmpty().withMessage('كلمة المرور مطلوبة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('auth/login', {
        title: 'تسجيل الدخول',
        layout: 'auth',
        errors: errors.array(),
        formData: req.body
      });
    }

    const { email, password } = req.body;
    
    // Find user by email
    const user = await User.findByEmail(email);
    if (!user || !user.isActive) {
      return res.render('auth/login', {
        title: 'تسجيل الدخول',
        layout: 'auth',
        error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        formData: req.body
      });
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.render('auth/login', {
        title: 'تسجيل الدخول',
        layout: 'auth',
        error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        formData: req.body
      });
    }

    // Create session
    req.session.user = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    };

    // Redirect based on role
    switch (user.role) {
      case 'admin':
        return res.redirect('/admin/dashboard');
      case 'general_manager':
        return res.redirect('/general-manager/dashboard');
      case 'venue_manager':
        return res.redirect('/venue-manager/dashboard');
      case 'party_owner':
        return res.redirect('/party-owner/dashboard');
      case 'scanner':
        return res.redirect('/scanner/dashboard');
      default:
        return res.redirect('/');
    }
  } catch (error) {
    console.error('Login error:', error);
    res.render('auth/login', {
      title: 'تسجيل الدخول',
      layout: 'auth',
      error: 'حدث خطأ أثناء تسجيل الدخول',
      formData: req.body
    });
  }
});

// Register page (only for party owners)
router.get('/register', redirectIfAuthenticated, (req, res) => {
  res.render('auth/register', {
    title: 'إنشاء حساب جديد',
    layout: 'auth'
  });
});

// Register process
router.post('/register', [
  body('fullName').isLength({ min: 2 }).withMessage('الاسم الكامل مطلوب'),
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.password) {
      throw new Error('تأكيد كلمة المرور غير متطابق');
    }
    return true;
  }),
  body('phone').optional().isMobilePhone('ar-SA').withMessage('رقم الهاتف غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('auth/register', {
        title: 'إنشاء حساب جديد',
        layout: 'auth',
        errors: errors.array(),
        formData: req.body
      });
    }

    const { fullName, email, password, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.render('auth/register', {
        title: 'إنشاء حساب جديد',
        layout: 'auth',
        error: 'البريد الإلكتروني مستخدم بالفعل',
        formData: req.body
      });
    }

    // Create new user
    const user = await User.create({
      fullName,
      email,
      password,
      phone: phone || null,
      role: 'party_owner'
    });

    // Create session
    req.session.user = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    };

    res.redirect('/party-owner/dashboard');
  } catch (error) {
    console.error('Registration error:', error);
    res.render('auth/register', {
      title: 'إنشاء حساب جديد',
      layout: 'auth',
      error: 'حدث خطأ أثناء إنشاء الحساب',
      formData: req.body
    });
  }
});

// Logout
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Logout error:', err);
    }
    res.redirect('/auth/login');
  });
});

module.exports = router;
