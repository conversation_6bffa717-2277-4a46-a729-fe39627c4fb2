{"version": 3, "file": "aead-aes-256-cbc-hmac-algorithm.js", "names": ["_types", "require", "_crypto", "_aeadAes256CbcHmacEncryptionKey", "algorithmName", "exports", "algorithmVersion", "blockSizeInBytes", "AeadAes256CbcHmac256Algorithm", "constructor", "columnEncryptionKey", "encryptionType", "keySizeInBytes", "keySize", "version", "<PERSON><PERSON><PERSON>", "from", "versionSize", "minimumCipherTextLengthInBytesNoAuthenticationTag", "minimumCipherTextLengthInBytesWithAuthenticationTag", "columnEncryptionkey", "isDeterministic", "SQLServerEncryptionType", "Deterministic", "encryptData", "plaintText", "iv", "hmacIv", "createHmac", "getIvKey", "update", "digest", "slice", "randomBytes", "encryptCipher", "createCipheriv", "getEncryptionKey", "encryptedBuffer", "concat", "final", "authenticationTag", "_prepareAuthenticationTag", "length", "decryptData", "cipherText", "alloc", "minimumCiperTextLength", "Error", "startIndex", "toString", "authenticationTagOffset", "copy", "cipherTextOffset", "cipherTextCount", "compare", "Math", "min", "plainText", "decipher", "createDecipheriv", "error", "message", "offset", "hmac", "getMacKey"], "sources": ["../../src/always-encrypted/aead-aes-256-cbc-hmac-algorithm.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { type EncryptionAlgorithm, SQLServerEncryptionType } from './types';\nimport { createHmac, randomBytes, createCipheriv, createDecipheriv } from 'crypto';\nimport { AeadAes256CbcHmac256EncryptionKey, keySize } from './aead-aes-256-cbc-hmac-encryption-key';\n\nexport const algorithmName = 'AEAD_AES_256_CBC_HMAC_SHA256';\nconst algorithmVersion = 0x1;\nconst blockSizeInBytes = 16;\n\nexport class AeadAes256CbcHmac256Algorithm implements EncryptionAlgorithm {\n  declare private columnEncryptionkey: AeadAes256CbcHmac256EncryptionKey;\n  declare private isDeterministic: boolean;\n  declare private keySizeInBytes: number;\n  declare private version: Buffer;\n  declare private versionSize: Buffer;\n  declare private minimumCipherTextLengthInBytesNoAuthenticationTag: number;\n  declare private minimumCipherTextLengthInBytesWithAuthenticationTag: number;\n\n  constructor(columnEncryptionKey: AeadAes256CbcHmac256EncryptionKey, encryptionType: SQLServerEncryptionType) {\n    this.keySizeInBytes = keySize / 8;\n    this.version = Buffer.from([algorithmVersion]);\n    this.versionSize = Buffer.from([1]);\n    this.minimumCipherTextLengthInBytesNoAuthenticationTag = 1 + blockSizeInBytes + blockSizeInBytes;\n    this.minimumCipherTextLengthInBytesWithAuthenticationTag = this.minimumCipherTextLengthInBytesNoAuthenticationTag + this.keySizeInBytes;\n    this.columnEncryptionkey = columnEncryptionKey;\n\n    this.isDeterministic = encryptionType === SQLServerEncryptionType.Deterministic;\n  }\n\n  encryptData(plaintText: Buffer): Buffer {\n    let iv: Buffer;\n\n    if (this.isDeterministic === true) {\n      const hmacIv = createHmac('sha256', this.columnEncryptionkey.getIvKey());\n      hmacIv.update(plaintText);\n      iv = hmacIv.digest().slice(0, blockSizeInBytes);\n    } else {\n      iv = randomBytes(blockSizeInBytes);\n    }\n\n    const encryptCipher = createCipheriv('aes-256-cbc', this.columnEncryptionkey.getEncryptionKey(), iv);\n\n    const encryptedBuffer = Buffer.concat([encryptCipher.update(plaintText), encryptCipher.final()]);\n\n    const authenticationTag: Buffer = this._prepareAuthenticationTag(iv, encryptedBuffer, 0, encryptedBuffer.length);\n\n    return Buffer.concat([Buffer.from([algorithmVersion]), authenticationTag, iv, encryptedBuffer]);\n  }\n\n  decryptData(cipherText: Buffer): Buffer {\n    const iv: Buffer = Buffer.alloc(blockSizeInBytes);\n\n    const minimumCiperTextLength: number = this.minimumCipherTextLengthInBytesWithAuthenticationTag;\n\n    if (cipherText.length < minimumCiperTextLength) {\n      throw new Error(`Specified ciphertext has an invalid size of ${cipherText.length} bytes, which is below the minimum ${minimumCiperTextLength} bytes required for decryption.`);\n    }\n\n    let startIndex = 0;\n    if (cipherText[0] !== algorithmVersion) {\n      throw new Error(`The specified ciphertext's encryption algorithm version ${Buffer.from([cipherText[0]]).toString('hex')} does not match the expected encryption algorithm version ${algorithmVersion}.`);\n    }\n\n    startIndex += 1;\n    let authenticationTagOffset = 0;\n\n    authenticationTagOffset = startIndex;\n    startIndex += this.keySizeInBytes;\n\n    cipherText.copy(iv, 0, startIndex, startIndex + iv.length);\n    startIndex += iv.length;\n\n    const cipherTextOffset = startIndex;\n    const cipherTextCount = cipherText.length - startIndex;\n\n    const authenticationTag: Buffer = this._prepareAuthenticationTag(iv, cipherText, cipherTextOffset, cipherTextCount);\n\n    if (0 !== authenticationTag.compare(cipherText, authenticationTagOffset, Math.min(authenticationTagOffset + cipherTextCount, authenticationTagOffset + authenticationTag.length), 0, Math.min(cipherTextCount, authenticationTag.length))) {\n      throw new Error('Specified ciphertext has an invalid authentication tag.');\n    }\n\n    let plainText: Buffer;\n\n    const decipher = createDecipheriv('aes-256-cbc', this.columnEncryptionkey.getEncryptionKey(), iv);\n    try {\n      plainText = decipher.update(cipherText.slice(cipherTextOffset, cipherTextOffset + cipherTextCount));\n      plainText = Buffer.concat([plainText, decipher.final()]);\n    } catch (error: any) {\n      throw new Error(`Internal error while decryption: ${error.message}`);\n    }\n\n    return plainText;\n  }\n\n  _prepareAuthenticationTag(iv: Buffer, cipherText: Buffer, offset: number, length: number): Buffer {\n    const hmac = createHmac('sha256', this.columnEncryptionkey.getMacKey());\n\n    hmac.update(this.version);\n    hmac.update(iv);\n    hmac.update(cipherText.slice(offset, offset + length));\n    hmac.update(this.versionSize);\n    return hmac.digest();\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,+BAAA,GAAAF,OAAA;AALA;AACA;;AAMO,MAAMG,aAAa,GAAG,8BAA8B;AAACC,OAAA,CAAAD,aAAA,GAAAA,aAAA;AAC5D,MAAME,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,gBAAgB,GAAG,EAAE;AAEpB,MAAMC,6BAA6B,CAAgC;EASxEC,WAAWA,CAACC,mBAAsD,EAAEC,cAAuC,EAAE;IAC3G,IAAI,CAACC,cAAc,GAAGC,uCAAO,GAAG,CAAC;IACjC,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC,CAACV,gBAAgB,CAAC,CAAC;IAC9C,IAAI,CAACW,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,IAAI,CAACE,iDAAiD,GAAG,CAAC,GAAGX,gBAAgB,GAAGA,gBAAgB;IAChG,IAAI,CAACY,mDAAmD,GAAG,IAAI,CAACD,iDAAiD,GAAG,IAAI,CAACN,cAAc;IACvI,IAAI,CAACQ,mBAAmB,GAAGV,mBAAmB;IAE9C,IAAI,CAACW,eAAe,GAAGV,cAAc,KAAKW,8BAAuB,CAACC,aAAa;EACjF;EAEAC,WAAWA,CAACC,UAAkB,EAAU;IACtC,IAAIC,EAAU;IAEd,IAAI,IAAI,CAACL,eAAe,KAAK,IAAI,EAAE;MACjC,MAAMM,MAAM,GAAG,IAAAC,kBAAU,EAAC,QAAQ,EAAE,IAAI,CAACR,mBAAmB,CAACS,QAAQ,CAAC,CAAC,CAAC;MACxEF,MAAM,CAACG,MAAM,CAACL,UAAU,CAAC;MACzBC,EAAE,GAAGC,MAAM,CAACI,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAEzB,gBAAgB,CAAC;IACjD,CAAC,MAAM;MACLmB,EAAE,GAAG,IAAAO,mBAAW,EAAC1B,gBAAgB,CAAC;IACpC;IAEA,MAAM2B,aAAa,GAAG,IAAAC,sBAAc,EAAC,aAAa,EAAE,IAAI,CAACf,mBAAmB,CAACgB,gBAAgB,CAAC,CAAC,EAAEV,EAAE,CAAC;IAEpG,MAAMW,eAAe,GAAGtB,MAAM,CAACuB,MAAM,CAAC,CAACJ,aAAa,CAACJ,MAAM,CAACL,UAAU,CAAC,EAAES,aAAa,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IAEhG,MAAMC,iBAAyB,GAAG,IAAI,CAACC,yBAAyB,CAACf,EAAE,EAAEW,eAAe,EAAE,CAAC,EAAEA,eAAe,CAACK,MAAM,CAAC;IAEhH,OAAO3B,MAAM,CAACuB,MAAM,CAAC,CAACvB,MAAM,CAACC,IAAI,CAAC,CAACV,gBAAgB,CAAC,CAAC,EAAEkC,iBAAiB,EAAEd,EAAE,EAAEW,eAAe,CAAC,CAAC;EACjG;EAEAM,WAAWA,CAACC,UAAkB,EAAU;IACtC,MAAMlB,EAAU,GAAGX,MAAM,CAAC8B,KAAK,CAACtC,gBAAgB,CAAC;IAEjD,MAAMuC,sBAA8B,GAAG,IAAI,CAAC3B,mDAAmD;IAE/F,IAAIyB,UAAU,CAACF,MAAM,GAAGI,sBAAsB,EAAE;MAC9C,MAAM,IAAIC,KAAK,CAAE,+CAA8CH,UAAU,CAACF,MAAO,sCAAqCI,sBAAuB,iCAAgC,CAAC;IAChL;IAEA,IAAIE,UAAU,GAAG,CAAC;IAClB,IAAIJ,UAAU,CAAC,CAAC,CAAC,KAAKtC,gBAAgB,EAAE;MACtC,MAAM,IAAIyC,KAAK,CAAE,2DAA0DhC,MAAM,CAACC,IAAI,CAAC,CAAC4B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAACK,QAAQ,CAAC,KAAK,CAAE,6DAA4D3C,gBAAiB,GAAE,CAAC;IAC1M;IAEA0C,UAAU,IAAI,CAAC;IACf,IAAIE,uBAAuB,GAAG,CAAC;IAE/BA,uBAAuB,GAAGF,UAAU;IACpCA,UAAU,IAAI,IAAI,CAACpC,cAAc;IAEjCgC,UAAU,CAACO,IAAI,CAACzB,EAAE,EAAE,CAAC,EAAEsB,UAAU,EAAEA,UAAU,GAAGtB,EAAE,CAACgB,MAAM,CAAC;IAC1DM,UAAU,IAAItB,EAAE,CAACgB,MAAM;IAEvB,MAAMU,gBAAgB,GAAGJ,UAAU;IACnC,MAAMK,eAAe,GAAGT,UAAU,CAACF,MAAM,GAAGM,UAAU;IAEtD,MAAMR,iBAAyB,GAAG,IAAI,CAACC,yBAAyB,CAACf,EAAE,EAAEkB,UAAU,EAAEQ,gBAAgB,EAAEC,eAAe,CAAC;IAEnH,IAAI,CAAC,KAAKb,iBAAiB,CAACc,OAAO,CAACV,UAAU,EAAEM,uBAAuB,EAAEK,IAAI,CAACC,GAAG,CAACN,uBAAuB,GAAGG,eAAe,EAAEH,uBAAuB,GAAGV,iBAAiB,CAACE,MAAM,CAAC,EAAE,CAAC,EAAEa,IAAI,CAACC,GAAG,CAACH,eAAe,EAAEb,iBAAiB,CAACE,MAAM,CAAC,CAAC,EAAE;MACzO,MAAM,IAAIK,KAAK,CAAC,yDAAyD,CAAC;IAC5E;IAEA,IAAIU,SAAiB;IAErB,MAAMC,QAAQ,GAAG,IAAAC,wBAAgB,EAAC,aAAa,EAAE,IAAI,CAACvC,mBAAmB,CAACgB,gBAAgB,CAAC,CAAC,EAAEV,EAAE,CAAC;IACjG,IAAI;MACF+B,SAAS,GAAGC,QAAQ,CAAC5B,MAAM,CAACc,UAAU,CAACZ,KAAK,CAACoB,gBAAgB,EAAEA,gBAAgB,GAAGC,eAAe,CAAC,CAAC;MACnGI,SAAS,GAAG1C,MAAM,CAACuB,MAAM,CAAC,CAACmB,SAAS,EAAEC,QAAQ,CAACnB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC,OAAOqB,KAAU,EAAE;MACnB,MAAM,IAAIb,KAAK,CAAE,oCAAmCa,KAAK,CAACC,OAAQ,EAAC,CAAC;IACtE;IAEA,OAAOJ,SAAS;EAClB;EAEAhB,yBAAyBA,CAACf,EAAU,EAAEkB,UAAkB,EAAEkB,MAAc,EAAEpB,MAAc,EAAU;IAChG,MAAMqB,IAAI,GAAG,IAAAnC,kBAAU,EAAC,QAAQ,EAAE,IAAI,CAACR,mBAAmB,CAAC4C,SAAS,CAAC,CAAC,CAAC;IAEvED,IAAI,CAACjC,MAAM,CAAC,IAAI,CAAChB,OAAO,CAAC;IACzBiD,IAAI,CAACjC,MAAM,CAACJ,EAAE,CAAC;IACfqC,IAAI,CAACjC,MAAM,CAACc,UAAU,CAACZ,KAAK,CAAC8B,MAAM,EAAEA,MAAM,GAAGpB,MAAM,CAAC,CAAC;IACtDqB,IAAI,CAACjC,MAAM,CAAC,IAAI,CAACb,WAAW,CAAC;IAC7B,OAAO8C,IAAI,CAAChC,MAAM,CAAC,CAAC;EACtB;AACF;AAAC1B,OAAA,CAAAG,6BAAA,GAAAA,6BAAA"}