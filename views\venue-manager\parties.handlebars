<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إدارة الحفلات - {{managedVenue.name}}</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/venue-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">إدارة الحفلات</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  {{#if success}}
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    {{success}}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  {{/if}}

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-4">
              <a href="/venue-manager/parties/create" class="btn btn-primary mb-2">
                <i class="mdi mdi-plus-circle me-1"></i>
                إنشاء حفلة جديدة
              </a>
            </div>
            <div class="col-sm-8">
              <div class="text-sm-end">
                <div class="d-flex gap-2">
                  <select class="form-select" id="statusFilter" onchange="filterParties()">
                    <option value="">جميع الحالات</option>
                    <option value="planned">مخطط</option>
                    <option value="confirmed">مؤكد</option>
                    <option value="active">نشط</option>
                    <option value="completed">مكتمل</option>
                    <option value="cancelled">ملغي</option>
                  </select>
                  <input type="text" class="form-control" id="searchInput" placeholder="البحث في الحفلات..." onkeyup="searchParties()">
                </div>
              </div>
            </div>
          </div>

          <div class="table-responsive">
            <table class="table table-centered table-striped dt-responsive nowrap w-100" id="partiesTable">
              <thead>
                <tr>
                  <th>عنوان الحفلة</th>
                  <th>صاحب الحفلة</th>
                  <th>النوع</th>
                  <th>تاريخ الحفلة</th>
                  <th>الوقت</th>
                  <th>عدد الضيوف</th>
                  <th>التكلفة</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {{#each parties}}
                <tr data-status="{{status}}">
                  <td>
                    <strong>{{title}}</strong>
                    {{#if description}}
                    <br><small class="text-muted">{{truncate description 50}}</small>
                    {{/if}}
                  </td>
                  <td>
                    {{owner.fullName}}
                    <br><small class="text-muted">{{owner.email}}</small>
                  </td>
                  <td>
                    <span class="badge bg-light text-dark">{{getPartyTypeText type}}</span>
                  </td>
                  <td>{{formatDate partyDate}}</td>
                  <td>{{startTime}} - {{endTime}}</td>
                  <td>
                    {{expectedGuests}}
                    {{#if actualGuests}}
                    <br><small class="text-success">حضر: {{actualGuests}}</small>
                    {{/if}}
                  </td>
                  <td>
                    {{#if totalCost}}
                    {{totalCost}} ريال
                    {{else}}
                    -
                    {{/if}}
                  </td>
                  <td>
                    {{#eq status 'planned'}}
                    <span class="badge bg-secondary">مخطط</span>
                    {{/eq}}
                    {{#eq status 'confirmed'}}
                    <span class="badge bg-info">مؤكد</span>
                    {{/eq}}
                    {{#eq status 'active'}}
                    <span class="badge bg-success">نشط</span>
                    {{/eq}}
                    {{#eq status 'completed'}}
                    <span class="badge bg-primary">مكتمل</span>
                    {{/eq}}
                    {{#eq status 'cancelled'}}
                    <span class="badge bg-danger">ملغي</span>
                    {{/eq}}
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewParty('{{id}}')" title="عرض التفاصيل">
                        <i class="mdi mdi-eye"></i>
                      </button>
                      {{#if (canModifyParty status)}}
                      <button type="button" class="btn btn-sm btn-outline-warning" onclick="editParty('{{id}}')" title="تعديل">
                        <i class="mdi mdi-pencil"></i>
                      </button>
                      {{/if}}
                      {{#eq status 'planned'}}
                      <button type="button" class="btn btn-sm btn-outline-success" onclick="confirmParty('{{id}}')" title="تأكيد الحفلة">
                        <i class="mdi mdi-check"></i>
                      </button>
                      {{/eq}}
                      {{#eq status 'confirmed'}}
                      <button type="button" class="btn btn-sm btn-outline-info" onclick="activateParty('{{id}}')" title="تفعيل الحفلة">
                        <i class="mdi mdi-play"></i>
                      </button>
                      {{/eq}}
                      {{#eq status 'active'}}
                      <button type="button" class="btn btn-sm btn-outline-primary" onclick="completeParty('{{id}}')" title="إنهاء الحفلة">
                        <i class="mdi mdi-stop"></i>
                      </button>
                      {{/eq}}
                      {{#if (canCancelParty status)}}
                      <button type="button" class="btn btn-sm btn-outline-danger" onclick="cancelParty('{{id}}')" title="إلغاء الحفلة">
                        <i class="mdi mdi-close"></i>
                      </button>
                      {{/if}}
                    </div>
                  </td>
                </tr>
                {{else}}
                <tr>
                  <td colspan="9" class="text-center">لا توجد حفلات</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>

          {{#if pagination}}
          <div class="row">
            <div class="col-12">
              <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                  {{#if pagination.hasPrev}}
                  <li class="page-item">
                    <a class="page-link" href="?page={{subtract pagination.currentPage 1}}">السابق</a>
                  </li>
                  {{/if}}
                  
                  <li class="page-item active">
                    <span class="page-link">{{pagination.currentPage}} من {{pagination.totalPages}}</span>
                  </li>
                  
                  {{#if pagination.hasNext}}
                  <li class="page-item">
                    <a class="page-link" href="?page={{add pagination.currentPage 1}}">التالي</a>
                  </li>
                  {{/if}}
                </ul>
              </nav>
            </div>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Party Details Modal -->
<div class="modal fade" id="partyModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل الحفلة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="partyModalBody">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

<script>
function viewParty(partyId) {
  fetch(`/venue-manager/parties/${partyId}`)
    .then(response => response.json())
    .then(data => {
      document.getElementById('partyModalBody').innerHTML = `
        <div class="row">
          <div class="col-md-6">
            <h6>معلومات الحفلة</h6>
            <p><strong>العنوان:</strong> ${data.title}</p>
            <p><strong>النوع:</strong> ${getPartyTypeText(data.type)}</p>
            <p><strong>التاريخ:</strong> ${new Date(data.partyDate).toLocaleDateString('ar-SA')}</p>
            <p><strong>الوقت:</strong> ${data.startTime} - ${data.endTime}</p>
            <p><strong>عدد الضيوف المتوقع:</strong> ${data.expectedGuests}</p>
            ${data.actualGuests ? `<p><strong>عدد الضيوف الفعلي:</strong> ${data.actualGuests}</p>` : ''}
          </div>
          <div class="col-md-6">
            <h6>معلومات إضافية</h6>
            <p><strong>صاحب الحفلة:</strong> ${data.owner.fullName}</p>
            <p><strong>البريد الإلكتروني:</strong> ${data.owner.email}</p>
            ${data.totalCost ? `<p><strong>التكلفة:</strong> ${data.totalCost} ريال</p>` : ''}
            <p><strong>الحالة:</strong> ${getStatusText(data.status)}</p>
          </div>
        </div>
        ${data.description ? `<div class="row"><div class="col-12"><h6>الوصف</h6><p>${data.description}</p></div></div>` : ''}
        ${data.notes ? `<div class="row"><div class="col-12"><h6>ملاحظات</h6><p>${data.notes}</p></div></div>` : ''}
      `;
      new bootstrap.Modal(document.getElementById('partyModal')).show();
    })
    .catch(error => {
      console.error('Error:', error);
      alert('حدث خطأ في تحميل تفاصيل الحفلة');
    });
}

function editParty(partyId) {
  window.location.href = `/venue-manager/parties/${partyId}/edit`;
}

function confirmParty(partyId) {
  if (confirm('هل أنت متأكد من تأكيد هذه الحفلة؟')) {
    updatePartyStatus(partyId, 'confirmed');
  }
}

function activateParty(partyId) {
  if (confirm('هل أنت متأكد من تفعيل هذه الحفلة؟')) {
    updatePartyStatus(partyId, 'active');
  }
}

function completeParty(partyId) {
  if (confirm('هل أنت متأكد من إنهاء هذه الحفلة؟')) {
    updatePartyStatus(partyId, 'completed');
  }
}

function cancelParty(partyId) {
  if (confirm('هل أنت متأكد من إلغاء هذه الحفلة؟')) {
    updatePartyStatus(partyId, 'cancelled');
  }
}

function updatePartyStatus(partyId, status) {
  fetch(`/venue-manager/parties/${partyId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert(data.error || 'حدث خطأ');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في تحديث حالة الحفلة');
  });
}

function filterParties() {
  const filter = document.getElementById('statusFilter').value;
  const rows = document.querySelectorAll('#partiesTable tbody tr');
  
  rows.forEach(row => {
    if (!filter || row.dataset.status === filter) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

function searchParties() {
  const input = document.getElementById('searchInput').value.toLowerCase();
  const rows = document.querySelectorAll('#partiesTable tbody tr');
  
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    if (text.includes(input)) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

function getPartyTypeText(type) {
  const types = {
    'wedding': 'زفاف',
    'birthday': 'عيد ميلاد',
    'corporate': 'شركة',
    'graduation': 'تخرج',
    'anniversary': 'ذكرى سنوية',
    'other': 'أخرى'
  };
  return types[type] || 'أخرى';
}

function getStatusText(status) {
  const statuses = {
    'planned': 'مخطط',
    'confirmed': 'مؤكد',
    'active': 'نشط',
    'completed': 'مكتمل',
    'cancelled': 'ملغي'
  };
  return statuses[status] || status;
}
</script>
