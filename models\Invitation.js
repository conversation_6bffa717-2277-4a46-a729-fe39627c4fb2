const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const QRCode = require('qrcode');

const Invitation = sequelize.define('Invitation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  guestName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'guest_name',
    validate: {
      len: [2, 100]
    }
  },
  guestPhone: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'guest_phone',
    validate: {
      len: [10, 20]
    }
  },
  guestEmail: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'guest_email',
    validate: {
      isEmail: true
    }
  },
  qrCode: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'qr_code'
  },
  qrCodeImage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'qr_code_image',
    comment: 'Base64 encoded QR code image'
  },
  status: {
    type: DataTypes.ENUM('pending', 'sent', 'confirmed', 'attended', 'no_show', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending'
  },
  sentAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'sent_at'
  },
  confirmedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'confirmed_at'
  },
  attendedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'attended_at'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'ملاحظات خاصة بالضيف'
  },
  // Foreign Keys
  partyId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'party_id'
  },
  scannedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'scanned_by'
  }
}, {
  tableName: 'invitations',
  freezeTableName: true,
  timestamps: true,
  hooks: {
    beforeCreate: async (invitation) => {
      if (!invitation.qrCode) {
        invitation.qrCode = `INV-${invitation.id}-${Date.now()}`;
      }
      // Generate QR code image
      try {
        invitation.qrCodeImage = await QRCode.toDataURL(invitation.qrCode);
      } catch (error) {
        console.error('Error generating QR code:', error);
      }
    }
  }
});

// Associations
Invitation.associate = (models) => {
  // دعوة تنتمي لحفلة واحدة
  Invitation.belongsTo(models.Party, {
    foreignKey: 'partyId',
    as: 'party'
  });

  // دعوة تم مسحها بواسطة مستخدم
  Invitation.belongsTo(models.User, {
    foreignKey: 'scannedBy',
    as: 'scanner'
  });
};

// Instance methods
Invitation.prototype.markAsSent = function() {
  this.status = 'sent';
  this.sentAt = new Date();
  return this.save();
};

Invitation.prototype.markAsConfirmed = function() {
  this.status = 'confirmed';
  this.confirmedAt = new Date();
  return this.save();
};

Invitation.prototype.markAsAttended = function(scannerId) {
  this.status = 'attended';
  this.attendedAt = new Date();
  this.scannedBy = scannerId;
  return this.save();
};

Invitation.prototype.markAsNoShow = function() {
  this.status = 'no_show';
  return this.save();
};

Invitation.prototype.canBeScanned = function() {
  return ['sent', 'confirmed'].includes(this.status);
};

Invitation.prototype.regenerateQRCode = async function() {
  this.qrCode = `INV-${this.id}-${Date.now()}`;
  try {
    this.qrCodeImage = await QRCode.toDataURL(this.qrCode);
  } catch (error) {
    console.error('Error regenerating QR code:', error);
  }
  return this.save();
};

// Class methods
Invitation.findByQRCode = function(qrCode) {
  return this.findOne({
    where: { qrCode },
    include: [
      {
        model: require('./Party'),
        as: 'party',
        include: [
          { model: require('./Venue'), as: 'venue' },
          { model: require('./User'), as: 'owner' }
        ]
      }
    ]
  });
};

Invitation.findByParty = function(partyId) {
  return this.findAll({
    where: { partyId },
    order: [['guestName', 'ASC']]
  });
};

Invitation.findAttendedByParty = function(partyId) {
  return this.findAll({
    where: {
      partyId,
      status: 'attended'
    },
    order: [['attendedAt', 'ASC']]
  });
};

Invitation.getPartyStats = async function(partyId) {
  const stats = await this.findAll({
    where: { partyId },
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['status'],
    raw: true
  });

  const result = {
    total: 0,
    pending: 0,
    sent: 0,
    confirmed: 0,
    attended: 0,
    no_show: 0,
    cancelled: 0
  };

  stats.forEach(stat => {
    result[stat.status] = parseInt(stat.count);
    result.total += parseInt(stat.count);
  });

  return result;
};

module.exports = Invitation;
