const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Party = require('./Party');
const User = require('./User');

const Invitation = sequelize.define('Invitation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  guestName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'guest_name'
  },
  partyId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'party_id',
    references: {
      model: 'Parties',
      key: 'id'
    }
  },
  qrCode: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'qr_code'
  },
  status: {
    type: DataTypes.ENUM('sent', 'pending', 'attended', 'no_show'),
    allowNull: false,
    defaultValue: 'pending'
  },
  attendedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'attended_at'
  },
  scannedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'scanned_by',
    references: {
      model: 'Users',
      key: 'id'
    }
  }
}, {
  tableName: 'Invitations',
  freezeTableName: true
});

// Associations
Invitation.associate = (models) => {
  Invitation.belongsTo(models.Party, { foreignKey: 'partyId', as: 'party' });
  Invitation.belongsTo(models.User, { foreignKey: 'scannedBy', as: 'scanner' });
};

// Instance method
Invitation.prototype.markAsAttended = function(scannerId) {
  this.status = 'attended';
  this.attendedAt = new Date();
  this.scannedBy = scannerId;
  return this.save();
};

// Class method
Invitation.findByQRCode = function(qrCode) {
  return this.findOne({
    where: { qrCode },
    include: [
      { model: Party, as: 'party' }
    ]
  });
};

module.exports = Invitation;
