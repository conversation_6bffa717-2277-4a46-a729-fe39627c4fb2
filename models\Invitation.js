const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Party = require('./Party');

const Invitation = sequelize.define('Invitation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  Nameguest: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  PartyId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'party_id',
    references: {
      model: 'Parties',
      key: 'id'
    }
  },
  qrCode: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'qr_code'
  },
  status: {
    type: DataTypes.ENUM('sent', 'pending', 'attended', 'no_show'),
    allowNull: false,
    defaultValue: 'pending'
  },
  attendedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'attended_at'
  },
  scannedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'scanned_by',
    references: {
      model: 'Users',
      key: 'id'
    }
  }
}, {
  tableName: 'Invitations',
  freezeTableName: true
});

// Instance methods
Invitation.prototype.markAsAttended = function(scannerId) {
  this.status = 'attended';
  this.attendedAt = new Date();
  this.scannedBy = scannerId;
  return this.save();
};

// Class methods
Invitation.findByQRCode = function(qrCode) {
  return this.findOne({
    where: { qrCode },
    include: [
      {
        association: 'Guest',
        include: ['Party']
      }
    ]
  });
};

module.exports = Invitation;
