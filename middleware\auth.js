const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
    }
    return res.redirect('/auth/login');
  }
  next();
};

const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.session.user) {
      if (req.xhr || req.headers.accept.indexOf('json') > -1) {
        return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
      }
      return res.redirect('/auth/login');
    }

    if (!roles.includes(req.session.user.role)) {
      if (req.xhr || req.headers.accept.indexOf('json') > -1) {
        return res.status(403).json({ error: 'ليس لديك صلاحية للوصول لهذه الصفحة' });
      }
      return res.status(403).render('error', {
        title: 'غير مصرح',
        message: 'ليس لديك صلاحية للوصول لهذه الصفحة'
      });
    }
    next();
  };
};

const redirectIfAuthenticated = (req, res, next) => {
  if (req.session.user) {
    switch (req.session.user.role) {
      case 'admin':
        return res.redirect('/admin/dashboard');
      case 'general_manager':
        return res.redirect('/general-manager/dashboard');
      case 'venue_manager':
        return res.redirect('/venue-manager/dashboard');
      case 'party_owner':
        return res.redirect('/party-owner/dashboard');
      case 'scanner':
        return res.redirect('/scanner/dashboard');
      default:
        return res.redirect('/');
    }
  }
  next();
};

const checkPermission = (permission) => {
  return (req, res, next) => {
    const user = req.session.user;
    if (!user) {
      return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
    }

    const permissions = {
      admin: ['*'], // Admin has all permissions
      general_manager: [
        'create_venue',
        'manage_venue',
        'create_venue_manager',
        'manage_venue_managers',
        'view_all_reports',
        'manage_system'
      ],
      venue_manager: [
        'manage_venue',
        'create_party',
        'manage_party',
        'assign_party_owner',
        'assign_staff',
        'view_venue_reports'
      ],
      party_owner: [
        'manage_own_party',
        'manage_guests',
        'create_invitations',
        'view_own_reports'
      ],
      scanner: [
        'scan_qr',
        'update_attendance'
      ]
    };

    const userPermissions = permissions[user.role] || [];
    
    // Admin has all permissions
    if (userPermissions.includes('*')) {
      return next();
    }
    
    // Check specific permission
    if (userPermissions.includes(permission)) {
      return next();
    }

    return res.status(403).json({ error: 'ليس لديك صلاحية لهذا الإجراء' });
  };
};

module.exports = {
  requireAuth,
  requireRole,
  redirectIfAuthenticated,
  checkPermission
};
