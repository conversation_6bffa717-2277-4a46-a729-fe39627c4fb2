<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">تعيين ماسح ضوئي - {{managedVenue.name}}</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/venue-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="/venue-manager/staff">إدارة الموظفين</a></li>
            <li class="breadcrumb-item active">تعيين ماسح ضوئي</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8 offset-lg-2">
      <div class="card">
        <div class="card-body">
          {{#if error}}
          <div class="alert alert-danger" role="alert">
            {{error}}
          </div>
          {{/if}}

          {{#if errors}}
          <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
              {{#each errors}}
              <li>{{msg}}</li>
              {{/each}}
            </ul>
          </div>
          {{/if}}

          <!-- Venue Info -->
          <div class="alert alert-info" role="alert">
            <h6 class="alert-heading">معلومات القاعة</h6>
            <p class="mb-0">
              <strong>اسم القاعة:</strong> {{managedVenue.name}}<br>
              <strong>الموقع:</strong> {{managedVenue.location}}<br>
              <strong>السعة:</strong> {{managedVenue.capacity}} شخص
            </p>
          </div>

          {{#if availableScanners}}
          <form method="POST" action="/venue-manager/staff/assign">
            <div class="mb-4">
              <label for="scannerId" class="form-label">اختر الماسح الضوئي <span class="text-danger">*</span></label>
              <select class="form-select" id="scannerId" name="scannerId" required onchange="showScannerDetails()">
                <option value="">اختر ماسح ضوئي...</option>
                {{#each availableScanners}}
                <option value="{{id}}" data-name="{{fullName}}" data-email="{{email}}" data-phone="{{phone}}" {{#eq id ../formData.scannerId}}selected{{/eq}}>
                  {{fullName}} - {{email}}
                </option>
                {{/each}}
              </select>
            </div>

            <!-- Scanner Details -->
            <div id="scannerDetails" class="card bg-light mb-4" style="display: none;">
              <div class="card-body">
                <h6 class="card-title">تفاصيل الماسح الضوئي المختار</h6>
                <div class="row">
                  <div class="col-md-4">
                    <p class="mb-1"><strong>الاسم:</strong></p>
                    <p id="scannerName" class="text-muted">-</p>
                  </div>
                  <div class="col-md-4">
                    <p class="mb-1"><strong>البريد الإلكتروني:</strong></p>
                    <p id="scannerEmail" class="text-muted">-</p>
                  </div>
                  <div class="col-md-4">
                    <p class="mb-1"><strong>رقم الهاتف:</strong></p>
                    <p id="scannerPhone" class="text-muted">-</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Assignment Instructions -->
            <div class="alert alert-warning" role="alert">
              <h6 class="alert-heading">تعليمات التعيين</h6>
              <p class="mb-0">
                بعد تعيين الماسح الضوئي لهذه القاعة، سيتمكن من:
              </p>
              <ul class="mb-0 mt-2">
                <li>الوصول إلى واجهة المسح الضوئي</li>
                <li>مسح رموز QR للضيوف في حفلات هذه القاعة</li>
                <li>تحديث حالات الدعوات إلى "حضر"</li>
                <li>عرض قائمة الضيوف للحفلات النشطة</li>
              </ul>
            </div>

            <div class="text-end">
              <a href="/venue-manager/staff" class="btn btn-secondary me-2">إلغاء</a>
              <button type="submit" class="btn btn-primary">تعيين الماسح الضوئي</button>
            </div>
          </form>
          {{else}}
          <!-- No Available Scanners -->
          <div class="text-center py-4">
            <div class="avatar-lg mx-auto mb-4">
              <div class="avatar-title bg-soft-warning text-warning rounded-circle">
                <i class="mdi mdi-account-search font-24"></i>
              </div>
            </div>
            <h5>لا يوجد ماسحين ضوئيين متاحين</h5>
            <p class="text-muted">جميع الماسحين الضوئيين مُعيَّنين لقاعات أخرى أو لا يوجد ماسحين في النظام</p>
            
            <div class="mt-4">
              <h6>خيارات أخرى:</h6>
              <div class="d-flex justify-content-center gap-2">
                <button type="button" class="btn btn-outline-primary" onclick="showCreateScannerModal()">
                  <i class="mdi mdi-account-plus me-1"></i>
                  إنشاء ماسح ضوئي جديد
                </button>
                <a href="/venue-manager/staff" class="btn btn-secondary">
                  <i class="mdi mdi-arrow-left me-1"></i>
                  العودة لإدارة الموظفين
                </a>
              </div>
            </div>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Scanner Modal -->
<div class="modal fade" id="createScannerModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">إنشاء ماسح ضوئي جديد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="createScannerForm">
          <div class="mb-3">
            <label for="scannerFullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="scannerFullName" name="fullName" required>
          </div>
          <div class="mb-3">
            <label for="scannerEmail" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
            <input type="email" class="form-control" id="scannerEmail" name="email" required>
          </div>
          <div class="mb-3">
            <label for="scannerPhone" class="form-label">رقم الهاتف</label>
            <input type="tel" class="form-control" id="scannerPhone" name="phone" placeholder="05xxxxxxxx">
          </div>
          <div class="mb-3">
            <label for="scannerPassword" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
            <input type="password" class="form-control" id="scannerPassword" name="password" required minlength="6">
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="assignImmediately" name="assignImmediately" checked>
            <label class="form-check-label" for="assignImmediately">
              تعيين للقاعة فوراً بعد الإنشاء
            </label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="createScanner()">إنشاء وتعيين</button>
      </div>
    </div>
  </div>
</div>

<script>
function showScannerDetails() {
  const select = document.getElementById('scannerId');
  const selectedOption = select.options[select.selectedIndex];
  
  if (selectedOption.value) {
    document.getElementById('scannerName').textContent = selectedOption.dataset.name;
    document.getElementById('scannerEmail').textContent = selectedOption.dataset.email;
    document.getElementById('scannerPhone').textContent = selectedOption.dataset.phone || 'غير محدد';
    document.getElementById('scannerDetails').style.display = 'block';
  } else {
    document.getElementById('scannerDetails').style.display = 'none';
  }
}

function showCreateScannerModal() {
  new bootstrap.Modal(document.getElementById('createScannerModal')).show();
}

function createScanner() {
  const form = document.getElementById('createScannerForm');
  const formData = new FormData(form);

  fetch('/venue-manager/create-scanner', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      if (data.assigned) {
        // Scanner was created and assigned
        alert('تم إنشاء الماسح الضوئي وتعيينه للقاعة بنجاح');
        window.location.href = '/venue-manager/staff';
      } else {
        // Scanner was created but not assigned, add to select
        const select = document.getElementById('scannerId');
        const option = document.createElement('option');
        option.value = data.scanner.id;
        option.dataset.name = data.scanner.fullName;
        option.dataset.email = data.scanner.email;
        option.dataset.phone = data.scanner.phone || '';
        option.textContent = `${data.scanner.fullName} - ${data.scanner.email}`;
        option.selected = true;
        select.appendChild(option);
        
        // Show scanner details
        showScannerDetails();
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('createScannerModal')).hide();
        
        // Reset form
        form.reset();
        
        alert('تم إنشاء الماسح الضوئي بنجاح. يمكنك الآن تعيينه للقاعة.');
      }
    } else {
      alert(data.error || 'حدث خطأ في إنشاء الماسح الضوئي');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في إنشاء الماسح الضوئي');
  });
}

// Initialize scanner details if there's a pre-selected value
document.addEventListener('DOMContentLoaded', function() {
  showScannerDetails();
});
</script>
