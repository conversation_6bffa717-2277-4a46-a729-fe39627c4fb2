const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Venue = sequelize.define('Venue', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [2, 100]
    }
  },
  location: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  capacity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 1
    }
  },
  floor: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'رقم الطابق'
  },
  tableCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'table_count',
    comment: 'عدد الطاولات'
  },
  chairCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'chair_count',
    comment: 'عدد الكراسي'
  },
  pricePerHour: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'price_per_hour',
    comment: 'السعر بالساعة'
  },
  suiteName: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'suite_name',
    comment: 'اسم الجناح'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  amenities: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'المرافق المتاحة (مكيف، صوت، إضاءة، إلخ) - JSON string',
    get() {
      const value = this.getDataValue('amenities');
      return value ? JSON.parse(value) : null;
    },
    set(value) {
      this.setDataValue('amenities', value ? JSON.stringify(value) : null);
    }
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  // Foreign Keys
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'created_by',
    comment: 'المستخدم الذي أنشأ القاعة'
  }
}, {
  tableName: 'venues',
  freezeTableName: true,
  timestamps: true
});

// Associations
Venue.associate = (models) => {
  // قاعة لها مدير واحد
  Venue.hasOne(models.User, {
    foreignKey: 'managedVenueId',
    as: 'manager'
  });

  // قاعة تم إنشاؤها بواسطة مستخدم
  Venue.belongsTo(models.User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });

  // قاعة تستضيف عدة حفلات
  Venue.hasMany(models.Party, {
    foreignKey: 'venueId',
    as: 'parties'
  });
};

// Instance methods
Venue.prototype.isAvailable = async function(date, startTime, endTime, excludePartyId = null) {
  const Party = require('./Party');
  const whereClause = {
    venueId: this.id,
    partyDate: date,
    status: ['planned', 'active'],
    [sequelize.Op.or]: [
      {
        startTime: {
          [sequelize.Op.between]: [startTime, endTime]
        }
      },
      {
        endTime: {
          [sequelize.Op.between]: [startTime, endTime]
        }
      },
      {
        [sequelize.Op.and]: [
          { startTime: { [sequelize.Op.lte]: startTime } },
          { endTime: { [sequelize.Op.gte]: endTime } }
        ]
      }
    ]
  };

  if (excludePartyId) {
    whereClause.id = { [sequelize.Op.ne]: excludePartyId };
  }

  const conflictingParties = await Party.findAll({ where: whereClause });
  return conflictingParties.length === 0;
};

// Class methods
Venue.findActive = function() {
  return this.findAll({
    where: { isActive: true },
    order: [['name', 'ASC']]
  });
};

Venue.findByManager = function(managerId) {
  return this.findAll({
    include: [{
      model: require('./User'),
      as: 'manager',
      where: { id: managerId }
    }],
    where: { isActive: true }
  });
};

module.exports = Venue;
