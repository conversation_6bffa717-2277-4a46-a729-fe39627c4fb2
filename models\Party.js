const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Party = sequelize.define('Party', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  venueId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'venue_id',
    references: {
      model: 'Venues',
      key: 'id'
    }
  },
  ownerId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'owner_id',
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  partyDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    field: 'party_date'
  },
  startTime: {
    type: DataTypes.TIME,
    allowNull: false,
    field: 'start_time'
  },
  endTime: {
    type: DataTypes.TIME,
    allowNull: false,
    field: 'end_time'
  },
  expectedGuests: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'expected_guests',
    validate: {
      min: 1
    }
  },
  status: {
    type: DataTypes.ENUM('planned', 'active', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'planned'
  }
}, {
  tableName: 'Parties',
  freezeTableName: true,
  validate: {
    endTimeAfterStartTime() {
      if (this.endTime <= this.startTime) {
        throw new Error('وقت انتهاء الحفلة يجب أن يكون بعد وقت البداية');
      }
    }
  }
});

// Define associations
Party.associate = (models) => {
  Party.belongsTo(models.User, {
    foreignKey: 'ownerId',
    as: 'owner'
  });

  Party.belongsTo(models.Venue, {
    foreignKey: 'venueId',
    as: 'venue'
  });

  Party.hasMany(models.Invitation, {
    foreignKey: 'partyId',
    as: 'invitations'
  });
};

module.exports = Party;
