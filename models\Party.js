const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Party = sequelize.define('Party', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('wedding', 'birthday', 'corporate', 'graduation', 'anniversary', 'other'),
    allowNull: false,
    defaultValue: 'other'
  },
  venueId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'venue_id',
    references: {
      model: 'Venues',
      key: 'id'
    }
  },
  ownerId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'owner_id',
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  partyDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    field: 'party_date'
  },
  startTime: {
    type: DataTypes.TIME,
    allowNull: false,
    field: 'start_time'
  },
  endTime: {
    type: DataTypes.TIME,
    allowNull: false,
    field: 'end_time'
  },
  expectedGuests: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'expected_guests',
    validate: {
      min: 1
    }
  },
  actualGuests: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'actual_guests',
    defaultValue: 0,
    comment: 'عدد الضيوف الفعلي الذين حضروا'
  },
  totalCost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'total_cost',
    comment: 'التكلفة الإجمالية للحفلة'
  },
  status: {
    type: DataTypes.ENUM('planned', 'confirmed', 'active', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'planned'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'ملاحظات إضافية'
  }
}, {
  tableName: 'parties',
  freezeTableName: true,
  timestamps: true,
  validate: {
    endTimeAfterStartTime() {
      if (this.endTime <= this.startTime) {
        throw new Error('وقت انتهاء الحفلة يجب أن يكون بعد وقت البداية');
      }
    },
    actualGuestsNotExceedExpected() {
      if (this.actualGuests && this.actualGuests > this.expectedGuests) {
        throw new Error('عدد الضيوف الفعلي لا يمكن أن يتجاوز العدد المتوقع');
      }
    }
  }
});

// Associations
Party.associate = (models) => {
  // حفلة تنتمي لقاعة واحدة
  Party.belongsTo(models.Venue, {
    foreignKey: 'venueId',
    as: 'venue'
  });

  // حفلة تنتمي لمالك واحد
  Party.belongsTo(models.User, {
    foreignKey: 'ownerId',
    as: 'owner'
  });

  // حفلة تحتوي على عدة دعوات
  Party.hasMany(models.Invitation, {
    foreignKey: 'partyId',
    as: 'invitations'
  });
};

// Instance methods
Party.prototype.canBeModified = function() {
  return ['planned', 'confirmed'].includes(this.status);
};

Party.prototype.canBeActivated = function() {
  return this.status === 'confirmed';
};

Party.prototype.canBeCompleted = function() {
  return this.status === 'active';
};

Party.prototype.updateActualGuests = async function() {
  const Invitation = require('./Invitation');
  const attendedCount = await Invitation.count({
    where: {
      partyId: this.id,
      status: 'attended'
    }
  });

  this.actualGuests = attendedCount;
  return this.save();
};

// Class methods
Party.findByOwner = function(ownerId) {
  return this.findAll({
    where: { ownerId },
    order: [['partyDate', 'DESC'], ['startTime', 'DESC']]
  });
};

Party.findByVenue = function(venueId) {
  return this.findAll({
    where: { venueId },
    order: [['partyDate', 'DESC'], ['startTime', 'DESC']]
  });
};

Party.findUpcoming = function() {
  const { Op } = require('sequelize');
  return this.findAll({
    where: {
      partyDate: {
        [Op.gte]: new Date()
      },
      status: ['planned', 'confirmed', 'active']
    },
    order: [['partyDate', 'ASC'], ['startTime', 'ASC']]
  });
};

module.exports = Party;
