const sequelize = require('../config/database');
const { User, Venue, Party, Invitation } = require('../models');

async function seedDatabase() {
  try {
    console.log('🔄 بدء إدخال البيانات التجريبية...');

    // Create Admin User
    const admin = await User.create({
      fullName: 'مدير النظام',
      email: '<EMAIL>',
      password: 'admin123',
      phone: '0501234567',
      role: 'admin'
    });
    console.log('✅ تم إنشاء المدير');

    // Create Venue Managers
    const venueManager1 = await User.create({
      fullName: 'أحمد محمد',
      email: '<EMAIL>',
      password: 'manager123',
      phone: '0501234568',
      role: 'venue_manager'
    });

    const venueManager2 = await User.create({
      fullName: 'فاطمة علي',
      email: '<EMAIL>',
      password: 'manager123',
      phone: '0501234569',
      role: 'venue_manager'
    });
    console.log('✅ تم إنشاء مديري القاعات');

    // Create Party Owners
    const partyOwner1 = await User.create({
      fullName: 'محمد سالم',
      email: '<EMAIL>',
      password: 'owner123',
      phone: '0501234570',
      role: 'party_owner'
    });

    const partyOwner2 = await User.create({
      fullName: 'نورا أحمد',
      email: '<EMAIL>',
      password: 'owner123',
      phone: '0501234571',
      role: 'party_owner'
    });
    console.log('✅ تم إنشاء أصحاب الحفلات');

    // Create Scanners
    const scanner1 = await User.create({
      fullName: 'خالد عبدالله',
      email: '<EMAIL>',
      password: 'scanner123',
      phone: '0501234572',
      role: 'scanner'
    });

    const scanner2 = await User.create({
      fullName: 'سارة محمد',
      email: '<EMAIL>',
      password: 'scanner123',
      phone: '0501234573',
      role: 'scanner'
    });
    console.log('✅ تم إنشاء الماسحين');

    // Create Venues
    const venue1 = await Venue.create({
      name: 'قاعة الأمير',
      location: 'الرياض - حي الملك فهد',
      capacity: 500,
      floor: 1,
      tableCount: 50,
      chairCount: 500,
      pricePerHour: 2000,
      suiteName: 'جناح الأمير',
      description: 'قاعة فاخرة للمناسبات الكبيرة',
      amenities: JSON.stringify(['مكيف', 'صوت', 'إضاءة', 'مسرح', 'مطبخ']),
      createdBy: admin.id
    });

    const venue2 = await Venue.create({
      name: 'قاعة النخيل',
      location: 'جدة - حي الروضة',
      capacity: 300,
      floor: 2,
      tableCount: 30,
      chairCount: 300,
      pricePerHour: 1500,
      suiteName: 'جناح النخيل',
      description: 'قاعة عصرية للمناسبات المتوسطة',
      amenities: JSON.stringify(['مكيف', 'صوت', 'إضاءة', 'مطبخ']),
      createdBy: admin.id
    });

    const venue3 = await Venue.create({
      name: 'قاعة الياسمين',
      location: 'الدمام - حي الفيصلية',
      capacity: 200,
      floor: 1,
      tableCount: 20,
      chairCount: 200,
      pricePerHour: 1000,
      suiteName: 'جناح الياسمين',
      description: 'قاعة أنيقة للمناسبات الصغيرة',
      amenities: JSON.stringify(['مكيف', 'صوت', 'إضاءة']),
      createdBy: admin.id
    });
    console.log('✅ تم إنشاء القاعات');

    // Assign venues to managers
    await venueManager1.update({ managedVenueId: venue1.id });
    await venueManager2.update({ managedVenueId: venue2.id });
    console.log('✅ تم تعيين مديري القاعات');

    // Assign scanners to venues (through managedVenueId)
    await scanner1.update({ managedVenueId: venue1.id });
    await scanner2.update({ managedVenueId: venue2.id });
    console.log('✅ تم تعيين الماسحين للقاعات');

    // Create Parties
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const party1 = await Party.create({
      title: 'حفل زفاف أحمد وفاطمة',
      description: 'حفل زفاف في قاعة الأمير',
      type: 'wedding',
      venueId: venue1.id,
      ownerId: partyOwner1.id,
      partyDate: tomorrow.toISOString().split('T')[0],
      startTime: '19:00',
      endTime: '23:00',
      expectedGuests: 400,
      totalCost: 8000,
      status: 'confirmed',
      notes: 'حفل زفاف فاخر مع عشاء'
    });

    const party2 = await Party.create({
      title: 'حفل تخرج جامعة الملك سعود',
      description: 'حفل تخرج دفعة 2024',
      type: 'graduation',
      venueId: venue2.id,
      ownerId: partyOwner2.id,
      partyDate: nextWeek.toISOString().split('T')[0],
      startTime: '17:00',
      endTime: '21:00',
      expectedGuests: 250,
      totalCost: 6000,
      status: 'planned',
      notes: 'حفل تخرج مع كوكتيل'
    });

    const party3 = await Party.create({
      title: 'حفل عيد ميلاد الشركة',
      description: 'احتفال بالذكرى العاشرة للشركة',
      type: 'corporate',
      venueId: venue3.id,
      ownerId: partyOwner1.id,
      partyDate: today.toISOString().split('T')[0],
      startTime: '18:00',
      endTime: '22:00',
      expectedGuests: 150,
      totalCost: 4000,
      status: 'active',
      notes: 'حفل شركة مع عشاء عمل'
    });
    console.log('✅ تم إنشاء الحفلات');

    // Create Invitations for Party 1 (Wedding)
    const invitations1 = await Invitation.bulkCreate([
      {
        guestName: 'عبدالله أحمد',
        guestPhone: '0501111111',
        guestEmail: '<EMAIL>',
        partyId: party1.id,
        qrCode: `INV-${party1.id}-001-${Date.now()}`,
        status: 'sent',
        sentAt: new Date(),
        notes: 'صديق العريس'
      },
      {
        guestName: 'مريم محمد',
        guestPhone: '0501111112',
        guestEmail: '<EMAIL>',
        partyId: party1.id,
        qrCode: `INV-${party1.id}-002-${Date.now()}`,
        status: 'confirmed',
        sentAt: new Date(),
        confirmedAt: new Date(),
        notes: 'صديقة العروس'
      },
      {
        guestName: 'سعد علي',
        guestPhone: '0501111113',
        guestEmail: '<EMAIL>',
        partyId: party1.id,
        qrCode: `INV-${party1.id}-003-${Date.now()}`,
        status: 'sent',
        sentAt: new Date()
      },
      {
        guestName: 'هند سالم',
        guestPhone: '0501111114',
        guestEmail: '<EMAIL>',
        partyId: party1.id,
        qrCode: `INV-${party1.id}-004-${Date.now()}`,
        status: 'pending'
      },
      {
        guestName: 'يوسف خالد',
        guestPhone: '0501111115',
        guestEmail: '<EMAIL>',
        partyId: party1.id,
        qrCode: `INV-${party1.id}-005-${Date.now()}`,
        status: 'confirmed',
        sentAt: new Date(),
        confirmedAt: new Date()
      }
    ]);

    // Create Invitations for Party 3 (Active party)
    const invitations3 = await Invitation.bulkCreate([
      {
        guestName: 'راشد محمد',
        guestPhone: '0503333331',
        guestEmail: '<EMAIL>',
        partyId: party3.id,
        qrCode: `INV-${party3.id}-001-${Date.now()}`,
        status: 'attended',
        sentAt: new Date(),
        confirmedAt: new Date(),
        attendedAt: new Date(),
        scannedBy: scanner1.id
      },
      {
        guestName: 'نوال أحمد',
        guestPhone: '0503333332',
        guestEmail: '<EMAIL>',
        partyId: party3.id,
        qrCode: `INV-${party3.id}-002-${Date.now()}`,
        status: 'attended',
        sentAt: new Date(),
        confirmedAt: new Date(),
        attendedAt: new Date(),
        scannedBy: scanner1.id
      },
      {
        guestName: 'فهد سعد',
        guestPhone: '0503333333',
        guestEmail: '<EMAIL>',
        partyId: party3.id,
        qrCode: `INV-${party3.id}-003-${Date.now()}`,
        status: 'confirmed',
        sentAt: new Date(),
        confirmedAt: new Date()
      }
    ]);
    console.log('✅ تم إنشاء الدعوات');

    // Update actual guests count for active party
    await party3.updateActualGuests();

    console.log('\n📋 ملخص البيانات التجريبية:');
    console.log(`👤 المستخدمين: ${await User.count()}`);
    console.log(`🏢 القاعات: ${await Venue.count()}`);
    console.log(`🎉 الحفلات: ${await Party.count()}`);
    console.log(`🎫 الدعوات: ${await Invitation.count()}`);

    console.log('\n🔑 بيانات تسجيل الدخول:');
    console.log('المدير: <EMAIL> / admin123');
    console.log('مدير قاعة: <EMAIL> / manager123');
    console.log('صاحب حفلة: <EMAIL> / owner123');
    console.log('ماسح: <EMAIL> / scanner123');

  } catch (error) {
    console.error('❌ خطأ في إدخال البيانات:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedDatabase().then(() => {
    console.log('🎉 انتهت عملية إدخال البيانات بنجاح!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ فشلت عملية إدخال البيانات:', error);
    process.exit(1);
  });
}

module.exports = { seedDatabase };
