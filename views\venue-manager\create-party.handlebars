<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">إنشاء حفلة جديدة - {{managedVenue.name}}</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="/venue-manager/dashboard">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="/venue-manager/parties">إدارة الحفلات</a></li>
            <li class="breadcrumb-item active">إنشاء حفلة جديدة</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          {{#if error}}
          <div class="alert alert-danger" role="alert">
            {{error}}
          </div>
          {{/if}}

          {{#if errors}}
          <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
              {{#each errors}}
              <li>{{msg}}</li>
              {{/each}}
            </ul>
          </div>
          {{/if}}

          <!-- Venue Info -->
          <div class="alert alert-info" role="alert">
            <h6 class="alert-heading">معلومات القاعة</h6>
            <div class="row">
              <div class="col-md-3">
                <strong>اسم القاعة:</strong> {{managedVenue.name}}
              </div>
              <div class="col-md-3">
                <strong>السعة:</strong> {{managedVenue.capacity}} شخص
              </div>
              <div class="col-md-3">
                <strong>الطابق:</strong> {{#if managedVenue.floor}}{{managedVenue.floor}}{{else}}غير محدد{{/if}}
              </div>
              <div class="col-md-3">
                <strong>السعر/ساعة:</strong> {{#if managedVenue.pricePerHour}}{{managedVenue.pricePerHour}} ريال{{else}}غير محدد{{/if}}
              </div>
            </div>
          </div>

          <form method="POST" action="/venue-manager/parties/create" id="partyForm">
            <div class="row">
              <div class="col-md-8">
                <div class="mb-3">
                  <label for="title" class="form-label">عنوان الحفلة <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="title" name="title" value="{{formData.title}}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="type" class="form-label">نوع الحفلة <span class="text-danger">*</span></label>
                  <select class="form-select" id="type" name="type" required>
                    <option value="">اختر نوع الحفلة...</option>
                    <option value="wedding" {{#eq formData.type 'wedding'}}selected{{/eq}}>زفاف</option>
                    <option value="birthday" {{#eq formData.type 'birthday'}}selected{{/eq}}>عيد ميلاد</option>
                    <option value="corporate" {{#eq formData.type 'corporate'}}selected{{/eq}}>شركة</option>
                    <option value="graduation" {{#eq formData.type 'graduation'}}selected{{/eq}}>تخرج</option>
                    <option value="anniversary" {{#eq formData.type 'anniversary'}}selected{{/eq}}>ذكرى سنوية</option>
                    <option value="other" {{#eq formData.type 'other'}}selected{{/eq}}>أخرى</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="description" class="form-label">وصف الحفلة</label>
                  <textarea class="form-control" id="description" name="description" rows="3">{{formData.description}}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="ownerId" class="form-label">صاحب الحفلة <span class="text-danger">*</span></label>
                  <select class="form-select" id="ownerId" name="ownerId" required>
                    <option value="">اختر صاحب الحفلة...</option>
                    {{#each partyOwners}}
                    <option value="{{id}}" {{#eq id ../formData.ownerId}}selected{{/eq}}>{{fullName}} - {{email}}</option>
                    {{/each}}
                  </select>
                  <small class="form-text text-muted">
                    <a href="#" onclick="showCreateOwnerModal()">إنشاء صاحب حفلة جديد</a>
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="partyDate" class="form-label">تاريخ الحفلة <span class="text-danger">*</span></label>
                  <input type="date" class="form-control" id="partyDate" name="partyDate" value="{{formData.partyDate}}" required min="{{today}}">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="startTime" class="form-label">وقت البداية <span class="text-danger">*</span></label>
                  <input type="time" class="form-control" id="startTime" name="startTime" value="{{formData.startTime}}" required>
                </div>
              </div>
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="endTime" class="form-label">وقت النهاية <span class="text-danger">*</span></label>
                  <input type="time" class="form-control" id="endTime" name="endTime" value="{{formData.endTime}}" required>
                </div>
              </div>
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="expectedGuests" class="form-label">عدد الضيوف المتوقع <span class="text-danger">*</span></label>
                  <input type="number" class="form-control" id="expectedGuests" name="expectedGuests" value="{{formData.expectedGuests}}" min="1" max="{{managedVenue.capacity}}" required>
                  <small class="form-text text-muted">الحد الأقصى: {{managedVenue.capacity}} شخص</small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="totalCost" class="form-label">التكلفة الإجمالية (ريال)</label>
                  <input type="number" class="form-control" id="totalCost" name="totalCost" value="{{formData.totalCost}}" min="0" step="0.01">
                  <small class="form-text text-muted" id="suggestedCost"></small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="mb-3">
                  <label for="notes" class="form-label">ملاحظات إضافية</label>
                  <textarea class="form-control" id="notes" name="notes" rows="3">{{formData.notes}}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="text-end">
                  <a href="/venue-manager/parties" class="btn btn-secondary me-2">إلغاء</a>
                  <button type="submit" class="btn btn-primary">إنشاء الحفلة</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Party Owner Modal -->
<div class="modal fade" id="createOwnerModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">إنشاء صاحب حفلة جديد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="createOwnerForm">
          <div class="mb-3">
            <label for="ownerFullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="ownerFullName" name="fullName" required>
          </div>
          <div class="mb-3">
            <label for="ownerEmail" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
            <input type="email" class="form-control" id="ownerEmail" name="email" required>
          </div>
          <div class="mb-3">
            <label for="ownerPhone" class="form-label">رقم الهاتف</label>
            <input type="tel" class="form-control" id="ownerPhone" name="phone" placeholder="05xxxxxxxx">
          </div>
          <div class="mb-3">
            <label for="ownerPassword" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
            <input type="password" class="form-control" id="ownerPassword" name="password" required minlength="6">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="createPartyOwner()">إنشاء</button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Set minimum date to today
  const today = new Date().toISOString().split('T')[0];
  document.getElementById('partyDate').min = today;

  // Calculate suggested cost when time changes
  const startTime = document.getElementById('startTime');
  const endTime = document.getElementById('endTime');
  const pricePerHour = {{managedVenue.pricePerHour}};

  function calculateSuggestedCost() {
    if (startTime.value && endTime.value && pricePerHour) {
      const start = new Date(`2000-01-01T${startTime.value}`);
      const end = new Date(`2000-01-01T${endTime.value}`);
      const hours = (end - start) / (1000 * 60 * 60);
      
      if (hours > 0) {
        const suggestedCost = hours * pricePerHour;
        document.getElementById('suggestedCost').textContent = `مقترح: ${suggestedCost} ريال (${hours} ساعة × ${pricePerHour} ريال)`;
      }
    }
  }

  startTime.addEventListener('change', calculateSuggestedCost);
  endTime.addEventListener('change', calculateSuggestedCost);

  // Validate end time is after start time
  function validateTimes() {
    if (startTime.value && endTime.value) {
      if (endTime.value <= startTime.value) {
        endTime.setCustomValidity('وقت النهاية يجب أن يكون بعد وقت البداية');
      } else {
        endTime.setCustomValidity('');
      }
    }
  }

  startTime.addEventListener('change', validateTimes);
  endTime.addEventListener('change', validateTimes);

  // Check venue availability
  const partyDate = document.getElementById('partyDate');
  
  function checkAvailability() {
    if (partyDate.value && startTime.value && endTime.value) {
      fetch('/venue-manager/check-availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: partyDate.value,
          startTime: startTime.value,
          endTime: endTime.value
        })
      })
      .then(response => response.json())
      .then(data => {
        if (!data.available) {
          alert('القاعة غير متاحة في هذا الوقت. يرجى اختيار وقت آخر.');
        }
      })
      .catch(error => {
        console.error('Error checking availability:', error);
      });
    }
  }

  partyDate.addEventListener('change', checkAvailability);
  startTime.addEventListener('change', checkAvailability);
  endTime.addEventListener('change', checkAvailability);
});

function showCreateOwnerModal() {
  new bootstrap.Modal(document.getElementById('createOwnerModal')).show();
}

function createPartyOwner() {
  const form = document.getElementById('createOwnerForm');
  const formData = new FormData(form);

  fetch('/venue-manager/create-party-owner', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Add new owner to select
      const select = document.getElementById('ownerId');
      const option = document.createElement('option');
      option.value = data.owner.id;
      option.textContent = `${data.owner.fullName} - ${data.owner.email}`;
      option.selected = true;
      select.appendChild(option);
      
      // Close modal
      bootstrap.Modal.getInstance(document.getElementById('createOwnerModal')).hide();
      
      // Reset form
      form.reset();
    } else {
      alert(data.error || 'حدث خطأ في إنشاء صاحب الحفلة');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('حدث خطأ في إنشاء صاحب الحفلة');
  });
}
</script>
