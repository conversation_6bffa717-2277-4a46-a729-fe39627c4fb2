const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, Venue, Party, Invitation } = require('../models');
const { Op } = require('sequelize');

const router = express.Router();

// General Manager Dashboard
router.get('/dashboard', async (req, res) => {
  try {
    // Get statistics
    const [totalVenues, totalVenueManagers, totalParties, activeParties] = await Promise.all([
      Venue.count({ where: { isActive: true } }),
      User.count({ where: { role: 'venue_manager', isActive: true } }),
      Party.count(),
      Party.count({ where: { status: 'active' } })
    ]);

    // Get recent venues
    const recentVenues = await Venue.findAll({
      include: [
        { model: User, as: 'creator', attributes: ['fullName'] }
      ],
      order: [['createdAt', 'DESC']],
      limit: 5
    });

    // Get recent parties
    const recentParties = await Party.findAll({
      include: [
        { model: Venue, as: 'venue', attributes: ['name'] },
        { model: User, as: 'owner', attributes: ['fullName'] }
      ],
      order: [['createdAt', 'DESC']],
      limit: 5
    });

    res.render('general-manager/dashboard', {
      title: 'لوحة تحكم المدير العام',
      stats: {
        totalVenues,
        totalVenueManagers,
        totalParties,
        activeParties
      },
      recentVenues,
      recentParties
    });
  } catch (error) {
    console.error('General manager dashboard error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل لوحة التحكم'
    });
  }
});

// Venues Management
router.get('/venues', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;

    const { count, rows: venues } = await Venue.findAndCountAll({
      include: [
        { model: User, as: 'creator', attributes: ['fullName'] },
        { model: User, as: 'manager', attributes: ['fullName', 'email'] }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('general-manager/venues', {
      title: 'إدارة القاعات',
      venues,
      pagination: {
        currentPage: page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Venues list error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل قائمة القاعات'
    });
  }
});

// Create Venue Page
router.get('/venues/create', (req, res) => {
  res.render('general-manager/create-venue', {
    title: 'إنشاء قاعة جديدة'
  });
});

// Create Venue Process
router.post('/venues/create', [
  body('name').isLength({ min: 2 }).withMessage('اسم القاعة مطلوب'),
  body('location').isLength({ min: 5 }).withMessage('موقع القاعة مطلوب'),
  body('capacity').isInt({ min: 1 }).withMessage('سعة القاعة يجب أن تكون رقم صحيح أكبر من 0'),
  body('pricePerHour').optional().isFloat({ min: 0 }).withMessage('السعر يجب أن يكون رقم صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('general-manager/create-venue', {
        title: 'إنشاء قاعة جديدة',
        errors: errors.array(),
        formData: req.body
      });
    }

    const { 
      name, location, capacity, floor, tableCount, chairCount, 
      pricePerHour, suiteName, description, amenities 
    } = req.body;

    await Venue.create({
      name,
      location,
      capacity: parseInt(capacity),
      floor: floor ? parseInt(floor) : null,
      tableCount: tableCount ? parseInt(tableCount) : null,
      chairCount: chairCount ? parseInt(chairCount) : null,
      pricePerHour: pricePerHour ? parseFloat(pricePerHour) : null,
      suiteName: suiteName || null,
      description: description || null,
      amenities: amenities || null,
      createdBy: req.session.user.id
    });

    res.redirect('/general-manager/venues?success=تم إنشاء القاعة بنجاح');
  } catch (error) {
    console.error('Create venue error:', error);
    res.render('general-manager/create-venue', {
      title: 'إنشاء قاعة جديدة',
      error: 'حدث خطأ أثناء إنشاء القاعة',
      formData: req.body
    });
  }
});

// Venue Managers Management
router.get('/venue-managers', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;

    const { count, rows: venueManagers } = await User.findAndCountAll({
      where: { role: 'venue_manager' },
      include: [
        { model: Venue, as: 'managedVenue', attributes: ['name'] }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('general-manager/venue-managers', {
      title: 'إدارة مديري القاعات',
      venueManagers,
      pagination: {
        currentPage: page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Venue managers list error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل قائمة مديري القاعات'
    });
  }
});

// Create Venue Manager Page
router.get('/venue-managers/create', async (req, res) => {
  try {
    // Get available venues (without managers)
    const availableVenues = await Venue.findAll({
      where: { 
        isActive: true,
        '$manager.id$': null
      },
      include: [
        { model: User, as: 'manager', required: false }
      ],
      order: [['name', 'ASC']]
    });

    res.render('general-manager/create-venue-manager', {
      title: 'إنشاء مدير قاعة جديد',
      availableVenues
    });
  } catch (error) {
    console.error('Create venue manager page error:', error);
    res.render('error', {
      title: 'خطأ',
      message: 'حدث خطأ في تحميل صفحة إنشاء مدير القاعة'
    });
  }
});

// Create Venue Manager Process
router.post('/venue-managers/create', [
  body('fullName').isLength({ min: 2 }).withMessage('الاسم الكامل مطلوب'),
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('phone').optional().isMobilePhone('ar-SA').withMessage('رقم الهاتف غير صحيح'),
  body('managedVenueId').optional().isUUID().withMessage('القاعة المُدارة غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const availableVenues = await Venue.findAll({
        where: { 
          isActive: true,
          '$manager.id$': null
        },
        include: [
          { model: User, as: 'manager', required: false }
        ],
        order: [['name', 'ASC']]
      });
      
      return res.render('general-manager/create-venue-manager', {
        title: 'إنشاء مدير قاعة جديد',
        availableVenues,
        errors: errors.array(),
        formData: req.body
      });
    }

    const { fullName, email, password, phone, managedVenueId } = req.body;

    // Check if email already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      const availableVenues = await Venue.findAll({
        where: { 
          isActive: true,
          '$manager.id$': null
        },
        include: [
          { model: User, as: 'manager', required: false }
        ],
        order: [['name', 'ASC']]
      });
      
      return res.render('general-manager/create-venue-manager', {
        title: 'إنشاء مدير قاعة جديد',
        availableVenues,
        error: 'البريد الإلكتروني مستخدم بالفعل',
        formData: req.body
      });
    }

    await User.create({
      fullName,
      email,
      password,
      phone: phone || null,
      role: 'venue_manager',
      managedVenueId: managedVenueId || null
    });

    res.redirect('/general-manager/venue-managers?success=تم إنشاء مدير القاعة بنجاح');
  } catch (error) {
    console.error('Create venue manager error:', error);
    const availableVenues = await Venue.findAll({
      where: { 
        isActive: true,
        '$manager.id$': null
      },
      include: [
        { model: User, as: 'manager', required: false }
      ],
      order: [['name', 'ASC']]
    });
    
    res.render('general-manager/create-venue-manager', {
      title: 'إنشاء مدير قاعة جديد',
      availableVenues,
      error: 'حدث خطأ أثناء إنشاء مدير القاعة',
      formData: req.body
    });
  }
});

module.exports = router;
